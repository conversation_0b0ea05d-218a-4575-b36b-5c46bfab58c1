@import "src/assets/scss/theme/_mixins.scss";
@import "src/assets/scss/variables";

.field-wrapper {
  @include flex-content-align-center;
  margin-bottom: 20px;

  label {
    color: $black-shade-text !important;
    font-size: 16px !important;
    font-weight: 600;
    min-width: 200px;
    margin-bottom: 20px;
  }

  .field-content {
    @include flex-content-align-center;
    width: 100%;

    .dash {
      margin: 0px 3px;
    }

    .dot {
      display: block;
    }
  }

  .mat-error-position {
    position: relative;
  }

  .instructor-loader-wrapper {
    @include flex-content-align-center;
    margin-bottom: 20px;

    .instructor-loading {
      font-size: 14px;
      font-weight: 600;
    }

    .loader {
      margin-right: 8px;
      border-top: 2px solid $primary-color !important;
    }
  }
}

.field-with-mat-inputs {
  margin-bottom: 6px;
}

::ng-deep {
  .mat-select-custom {
    .mat-mdc-text-field-wrapper {
      height: 35px;
    }

    .mat-mdc-form-field-infix {
      padding-top: 5px !important;
      padding-bottom: 5px !important;
    }

    .mat-mdc-select-placeholder,
    .mat-mdc-select-value-text {
      font-size: 14px !important;
    }

    .multi-select-chips-wrapper {
      padding: 5px 15px !important;

      .select-placeholder {
        color: $gray-text;
        font-weight: normal !important;
      }
    }
  }

  .select-box-options-wrapper {
    .mdc-label {
      color: $black-shade-text !important;
    }
  }

  .mat-mdc-form-field-icon-suffix > .mat-icon {
    padding: 0px 5px 16px 0px !important;
  }

  .mat-drawer-inner-container {
    overflow: hidden !important;
  }

  .o-sidebar-wrapper .o-sidebar-body {
    overflow: auto;
    height: calc(100vh - 68px);
    padding: 20px 30px 10px 30px !important;
  }

  .sidenav-content-without-footer {
    overflow: hidden !important;
  }

  .mat-mdc-form-field-error-wrapper {
    padding: 0 !important;
  }

  .mat-start-date {
    .mat-mdc-text-field-wrapper,
    .mat-mdc-icon-button .mat-mdc-button-touch-target,
    .mat-mdc-button-ripple {
      height: 35px;
    }

    .mat-mdc-icon-button.mat-mdc-button-base {
      height: 35px;
      width: 35px;
    }

    .mat-mdc-icon-button .mat-mdc-button-ripple {
      height: 40px;
    }

    .mat-mdc-form-field-infix {
      padding-top: 5px !important;
      padding-bottom: 5px !important;
    }

    .mat-mdc-select-placeholder,
    .mat-mdc-select-value-text {
      font-size: 14px !important;
    }

    .mat-mdc-icon-button svg {
      height: 16px;
    }

    .mat-mdc-icon-button.mat-mdc-button-base {
      padding: 5px;
    }

    .mat-mdc-icon-button.mat-mdc-button-base {
      top: -9px;
    }

    .mat-datepicker-input {
      font-size: 14px !important;
    }

    .mat-mdc-icon-button .mat-mdc-button-persistent-ripple {
      border-radius: 9%;
    }

    .mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before {
      background: transparent !important;
    }
  }

  .mdc-list-item__primary-text {
    @include w-h-100;
  }
}

@media (max-width: 767px) {
  .action-btn-wrapper {
    display: flex;
  }

  .field-wrapper,
  .field-content {
    flex-wrap: wrap;

    .dash {
      display: none;
    }
  }
}
