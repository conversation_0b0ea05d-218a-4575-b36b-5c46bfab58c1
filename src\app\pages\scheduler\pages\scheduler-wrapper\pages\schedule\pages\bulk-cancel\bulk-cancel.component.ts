import { CommonModule, DatePipe } from '@angular/common';
import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { takeUntil } from 'rxjs';
import { AppToasterService, CommonService } from 'src/app/shared/services';
import { InstructorService } from 'src/app/schedule-introductory-lesson/services';
import { CBResponse, IdNameModel } from 'src/app/shared/models';
import { SchoolLocations } from 'src/app/pages/room-and-location-management/models';
import { MatSidenavModule } from '@angular/material/sidenav';
import { SharedModule } from 'src/app/shared/shared.module';
import { MatIconModule } from '@angular/material/icon';
import { MultiSelectChipsComponent } from '../../../../../../../../shared/components/multi-select-chips/multi-select-chips.component';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { BulkCancelFilter, BulkCancelForm } from '../../models';
import { MbscDatepickerChangeEvent, MbscDatepickerModule } from '@mobiscroll/angular';
import { provideNativeDateAdapter } from '@angular/material/core';
import { InstructorList } from 'src/app/schedule-introductory-lesson/models';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { CommonUtils } from 'src/app/shared/utils';
import { SupervisorFilter } from 'src/app/pages/members/pages/supervisors/models';
import { SchedulerService } from '../../services';
import moment from 'moment';
import { MatCheckboxChange, MatCheckboxModule } from '@angular/material/checkbox';
import { DateUtils } from 'src/app/shared/utils/date.utils';

const DEPENDENCIES = {
  MODULES: [
    MatButtonModule,
    CommonModule,
    MatDatepickerModule,
    MatInputModule,
    ReactiveFormsModule,
    MatIconModule,
    SharedModule,
    MatSidenavModule,
    MbscDatepickerModule,
    FormsModule,
    MatCheckboxModule
  ],
  COMPONENTS: [MultiSelectChipsComponent]
};

@Component({
  selector: 'app-bulk-cancel',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.COMPONENTS],
  providers: [provideNativeDateAdapter()],
  templateUrl: './bulk-cancel.component.html',
  styleUrls: ['./bulk-cancel.component.scss']
})
export class BulkCancelComponent extends BaseComponent implements OnInit {
  @Input() startDate!: Date;

  bulkCancelForm!: FormGroup<BulkCancelForm>;
  locations!: Array<SchoolLocations>;
  instructors!: Array<InstructorList>;
  minDate = new Date();
  selectedTime?: string;
  showMultiSelectChips = true;
  filters: BulkCancelFilter = {
    staff: {
      id: 1,
      defaultPlaceholder: 'Select Staff',
      placeholder: 'Select Staff',
      value: [] as Array<IdNameModel>,
      totalCount: 0,
      showMax: 3,
      isOpen: false,
      showSearchBar: true,
      options: [] as Array<IdNameModel>
    },

    location: {
      id: 2,
      defaultPlaceholder: 'Select location',
      placeholder: 'Select location',
      value: [] as Array<IdNameModel>,
      totalCount: 0,
      showMax: 1,
      isOpen: false,
      showSearchBar: true,
      options: [] as Array<IdNameModel>
    }
  };

  @Output() closeSideNav = new EventEmitter<void>();
  @Output() refreshScheduleData = new EventEmitter<void>();

  constructor(
    private readonly instructorService: InstructorService,
    private readonly commonService: CommonService,
    private readonly schedulerService: SchedulerService,
    private readonly toasterService: AppToasterService,
    private readonly datePipe: DatePipe,
    private readonly cdr: ChangeDetectorRef
  ) {
    super();
  }

  ngOnInit(): void {
    this.initializeForm();
    this.getInstructors();
    this.getLocations();
  }

  initializeForm(): void {
    this.bulkCancelForm = new FormGroup<BulkCancelForm>({
      scheduleStartDate: new FormControl('', { nonNullable: true, validators: [Validators.required] }),
      scheduleEndDate: new FormControl('', { nonNullable: true, validators: [Validators.required] }),
      scheduleStartTime: new FormControl('', { nonNullable: true }),
      scheduleEndTime: new FormControl('', { nonNullable: true }),
      locationIds: new FormControl([], { nonNullable: true }),
      instructorIds: new FormControl([], { nonNullable: true }),
      notes: new FormControl('', { nonNullable: true }),
      isCancelAll: new FormControl(false, { nonNullable: true }),
      isPassGenerated: new FormControl(false, { nonNullable: true }),
    });
  }

  setDateFilter(): void {
    const date = this.startDate.toISOString();
    this.setFormControlValue('scheduleStartDate', date);
    this.setFormControlValue('scheduleEndDate', date);
  }

  setTimeFilter(event: MbscDatepickerChangeEvent): void {
    this.selectedTime = event.valueText;
    const [startTime, endTime] = Array.isArray(event.value) ? event.value : [null, null];

    const formattedStartTime = this.datePipe.transform(startTime, this.constants.dateFormats.yyyy_MM_dd_T_HH_mm_ss);
    const formattedEndTime = this.datePipe.transform(endTime, this.constants.dateFormats.yyyy_MM_dd_T_HH_mm_ss);
    const combinedStartTime = CommonUtils.combineDateAndTime(this.startDate.toISOString(), formattedStartTime ?? '');
    const combinedEndTime = CommonUtils.combineDateAndTime(this.startDate.toISOString(), formattedEndTime ?? '');

    this.setFormControlValue('scheduleStartTime', combinedStartTime);
    this.setFormControlValue('scheduleEndTime', combinedEndTime);
  }

  setFormControlValue(controlName: string, value: string | number | boolean | null): void {
    (this.bulkCancelForm.controls as any)[controlName].setValue(value);
  }

  getFilterParamsForInstructors() {
    return CommonUtils.cleanObjectByRemovingKeysWithoutValue({
      SupervisorIdFilter: this.currentUser?.isSupervisor ? this.currentUser?.dependentId : null,
      isSupervisorFilter: SupervisorFilter.ALL,
      Page: 1
    });
  }

  getInstructors(): void {
    if (moment(this.startDate).isBefore(moment(this.minDate))) {
      this.startDate = new Date();
    }
    this.instructorService
      .add(this.getFilterParamsForInstructors(), `${API_URL.crud.getAll}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<InstructorList>) => {
          this.filters.staff.options = res.result.items.map(instructor => ({
            id: instructor.instructorDetail.id,
            name: instructor.instructorDetail.name
          }));
          this.cdr.detectChanges();
        }
      });
  }

  getLocations(): void {
    this.commonService
      .getLocations()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<SchoolLocations>) => {
          this.filters.location.options = res.result.items.map(location => ({
            id: location.schoolLocations.id,
            name: location.schoolLocations.locationName
          }));
          this.cdr.detectChanges();
        }
      });
  }

  onCancelAllCheckBoxChange(event: MatCheckboxChange): void {
    this.showMultiSelectChips = !event.checked;
    if (event.checked) {
      this.filters.staff.value = [];
      this.filters.location.value = [];
    }
  }

  checksBeforeSubmit(): boolean {
    if (!this.filters.staff.value.length && !this.filters.location.value.length && this.showMultiSelectChips) {
      this.toasterService.error(this.constants.errorMessages.selectionPending.replace('{item}', 'Staff or Location'));
      return true;
    }
    return false;
  }

  onCancel(): void {
    if (this.checksBeforeSubmit()) {
      return;
    }
    this.setDateFilter();
    if (this.bulkCancelForm.invalid) {
      this.bulkCancelForm.markAllAsTouched();
      return;
    }
    this.bulkCancelForm.markAsUntouched();
    this.showBtnLoader = true;
    this.schedulerService
      .add(
        {
          ...this.bulkCancelForm.getRawValue(),
          scheduleStartDate: DateUtils.getUtcRangeForLocalDate(this.bulkCancelForm.getRawValue().scheduleStartDate ?? '').startUtc,
          scheduleEndDate: DateUtils.getUtcRangeForLocalDate(this.bulkCancelForm.getRawValue().scheduleEndDate ?? '').endUtc,
          instructorIds: this.filters.staff.value.map(instructor => instructor.id),
          locationIds: this.filters.location.value.map(location => location.id)
        },
        API_URL.scheduleLessonDetails.bulkCancelScheduleLessons
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.onCloseSideNav();
          this.refreshScheduleData.emit();
          this.showBtnLoader = false;
          if (this.bulkCancelForm.getRawValue().isPassGenerated) {
            this.toasterService.success(this.constants.successMessages.lessonCanceledSuccess);
          }
          else {
            this.toasterService.success(this.constants.successMessages.canceledSuccessfully.replace('{item}', 'Lessons'));
          }
          this.cdr.detectChanges();
        },
        error: () => {
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  onCloseSideNav(): void {
    this.closeSideNav.emit();
  }
}
