<mat-sidenav-container class="example-container" [hasBackdrop]="true">
  @if (isUpdateMaintenanceRequest) {
  <mat-sidenav
    [opened]="isUpdateMaintenanceRequest"
    mode="over"
    position="end"
    fixedInViewport="true"
    [ngClass]="'sidebar-w-750'"
    [disableClose]="true"
  >
    <app-update-maintenance-request-status
      (closeModal)="onUpdateMaintenanceRequestStatus($event)"
      [selectedMaintenanceRequestDetails]="selectedMaintenanceRequestDetails"
    ></app-update-maintenance-request-status>
  </mat-sidenav>
  }
</mat-sidenav-container>

<div class="o-sidebar-wrapper">
  <div class="o-sidebar-header">
    <div class="title">Request Details</div>
    <div class="action-btn-wrapper">
      <button mat-raised-button color="accent" class="mat-accent-btn back-btn" type="button" (click)="onCloseModal()">Close</button>
      <button
        mat-raised-button
        color="primary"
        class="mat-primary-btn"
        type="button"
        *appHasPermission="[constants.roles.ADMIN, constants.roles.DESK_MANAGER]"
        (click)="closeMaintenanceRequestConfirmation(selectedMaintenanceRequestDetails.id)"
      >
        Close Request
      </button>
    </div>
  </div>
  <div class="divider"></div>
  <div class="o-sidebar-body">
    <ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : viewMaintenanceRequest"></ng-container>
  </div>
</div>

<ng-template #viewMaintenanceRequest>
  <div class="maintenance-request-class-detail mb-3">
    <div class="maintenance-request-class-content-wrapper title">
      <div class="name">
        {{ selectedMaintenanceRequestDetails.title }}
      </div>
      <div class="fw-600">
        <img
          [src]="constants.staticImages.icons.editPenGray"
          *appHasPermission="[constants.roles.ADMIN, constants.roles.DESK_MANAGER]"
          alt="pen"
          class="status-update"
          (click)="openUpdateMaintenanceRequestModal(selectedMaintenanceRequestDetails)"
          height="20"
        />
        @switch (selectedMaintenanceRequestDetails.status) { @case (maintenanceRequestsStatus.InProgress) {
        <span class="in-progress-request">{{ maintenanceRequestsStatusLabel.InProgress }}</span>
        } @case (maintenanceRequestsStatus.OnHold) {
        <span class="on-hold-request">{{ maintenanceRequestsStatusLabel.OnHold }}</span>
        } @case (maintenanceRequestsStatus.Open) {
        <span class="open-request">{{ maintenanceRequestsStatusLabel.Open }}</span>
        }@default {
        <span class="text-black">{{ maintenanceRequestsStatusLabel.Closed }}</span>
        } }
      </div>
    </div>
    <div class="maintenance-request-class-content-wrapper">
      <div>
        <div class="maintenance-request-class-content">
          <img [src]="constants.staticImages.icons.questionnaire" alt="" />
          <div class="info-label">Description</div>
        </div>
        <div class="info-content w-570">
          {{ selectedMaintenanceRequestDetails.description | dashIfEmpty }}
        </div>
      </div>
    </div>
    <div class="maintenance-request-class-content-wrapper">
      <div>
        <div class="maintenance-request-class-content">
          <img [src]="constants.staticImages.icons.status" alt="" />
          <div class="info-label">Request Type</div>
        </div>
        <div class="info-content w-570">
          {{ maintenanceRequestTypes[selectedMaintenanceRequestDetails.requestType] | dashIfEmpty }} 
          <span class="ms-1" *ngIf="selectedMaintenanceRequestDetails.requestType">Request</span>
        </div>
      </div>
    </div>
    <div class="maintenance-request-class-content-wrapper">
      <div>
        <div class="maintenance-request-class-content">
          <img [src]="constants.staticImages.icons.calendarIcon" alt="" />
          <div class="info-label">Created On</div>
        </div>
        <div class="info-content">
          {{ selectedMaintenanceRequestDetails.createdOn | localDate | dashIfEmpty | date : 'mediumDate' }}
          <div class="dot"></div>
          <div>
            <span class="text-gray me-2">From</span> <span>{{ selectedMaintenanceRequestDetails.createdByName }}</span>
          </div>
        </div>
      </div>
    </div>

    <div class="maintenance-request-class-content-wrapper">
      <div>
        <div class="maintenance-request-class-content">
          <img [src]="constants.staticImages.icons.location" alt="" />
          <div class="info-label">Location</div>
        </div>
        <div class="info-content">{{ selectedMaintenanceRequestDetails.locationName }}</div>
      </div>
    </div>

    <div class="maintenance-request-class-content-wrapper">
      <div>
        <div class="maintenance-request-class-content">
          <img [src]="constants.staticImages.icons.home" alt="" />
          <div class="info-label">Room Name</div>
        </div>
        <div class="info-content">{{ selectedMaintenanceRequestDetails.roomName | dashIfEmpty }}</div>
      </div>
    </div>

    <div class="maintenance-request-class-content-wrapper">
      <div>
        <div class="maintenance-request-class-content">
          <img [src]="constants.staticImages.icons.instrumentIcon" alt="" />
          <div class="info-label">Instrument Name</div>
        </div>
        <div class="info-content">{{ selectedMaintenanceRequestDetails.instrumentName | dashIfEmpty }}</div>
      </div>
    </div>
  </div>

  <div class="maintenance-request-class-content-wrapper" *ngIf="selectedMaintenanceRequestDetails.closedByName">
    <div>
      <div class="maintenance-request-class-content">
        <img [src]="constants.staticImages.icons.calendarIcon" alt="" />
        <div class="info-label">Closed On</div>
      </div>
      <div class="info-content">
        {{ selectedMaintenanceRequestDetails.closedOn | localDate | dashIfEmpty | date : 'mediumDate' }}
        <div class="dot"></div>
        <div>
          <span class="text-gray me-2">By</span> <span>{{ selectedMaintenanceRequestDetails.closedByName }}</span>
        </div>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #showLoader>
  <div class="page-loader-wrapper">
    <app-content-loader></app-content-loader>
  </div>
</ng-template>
