<div class="o-sidebar-wrapper">
  <div class="o-sidebar-header">
    <div class="back-btn-wrapper" (click)="closeSideNavFn()">
      <img [src]="constants.staticImages.icons.arrowLeft" class="pointer" alt="" />
      <div class="ps-2">
        <div class="title">Instructor</div>
        <div class="name">
          <img [src]="constants.staticImages.icons.profileIcon" class="pe-1" alt="" />
          {{ selectedSupervisorDetail?.name }}
        </div>
      </div>
    </div>
    <div class="action-btn-wrapper">
      <button mat-raised-button color="accent" class="mat-accent-btn back-btn" type="button" (click)="closeSideNavFn()">
        Close
      </button>
    </div>
  </div>
  <div class="divider"></div>
  <div class="o-sidebar-body">
    <div class="o-card">
      <div class="o-card-body">
        <div class="o-table">
          <div class="o-row o-header">
            <div class="o-cell first-cell">Instructor</div>
            <div class="o-cell">No. of Clients</div>
            <div class="o-cell">Location</div>
            <div class="o-cell">Instrument</div>
          </div>
          <div class="dotted-divider"></div>
          <div class="content">
            <ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : instructorTemplate"></ng-container>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<ng-template #instructorTemplate>
  <ng-container [ngTemplateOutlet]="instructors.length ? instructorTableContent : noDataFound"></ng-container>
</ng-template>

<ng-template #instructorTableContent>
  @for (instructor of instructors; track $index) {
    <div class="o-row">
      <div class="o-cell first-cell instructor-name-photo-wrapper">
        @if (instructor.instructorDetail.profilePhoto) {
          <img [src]="instructor.instructorDetail.profilePhoto" class="me-2" alt="" />
        } @else {
          <div class="placeholder-name">
            <div>
              {{ getInitials(instructor.instructorDetail.name) | uppercase }}
            </div>
          </div>
        }
        <div class="text-truncate" [matTooltip]="instructor.instructorDetail.name">{{ instructor.instructorDetail.name }}</div>
      </div>
      <div class="o-cell text-gray">{{ instructor.instructorDetail.students }}</div>
      <div class="o-cell text-gray">
        <div class="instrument-wrapper">
          @if (instructor.instructorDetail.instructorAvailability.length) {
            <img [src]="constants.staticImages.icons.location" class="me-1" alt="" />
            @for (instructorAvailability of instructor.instructorDetail.instructorAvailability; track $index) {
              <div class="instrument-item" *ngIf="$index < 1">{{ instructorAvailability.locationName }}</div>
              <div
                class="dot"
                *ngIf="
                  $index < 1 &&
                  getLocationDetails(instructor.instructorDetail.instructorAvailability).count - 1 !== $index
                "></div>
            }
            @if (getLocationDetails(instructor.instructorDetail.instructorAvailability).count > 1) {
              <div
                class="remaining-instrument-available-count"
                [matTooltip]="getLocationDetails(instructor.instructorDetail.instructorAvailability).names">
                {{ getLocationDetails(instructor.instructorDetail.instructorAvailability).count - 1 }}+
              </div>
            }
          } @else {
            -
          }
        </div>
      </div>
      <div class="o-cell text-gray">
        <div class="instrument-wrapper">
          @if (instructor.instructorDetail.instruments.length) {
            @for (instrument of instructor.instructorDetail.instruments; track $index) {
              <div class="instrument-item" *ngIf="$index < 1">{{ instrument.name }}</div>
              <div
                class="dot"
                *ngIf="$index < 1 && instructor.instructorDetail.instruments.length - 1 !== $index"></div>
            }
            @if (instructor.instructorDetail.instruments.length > 1) {
              <div
                class="remaining-instrument-available-count"
                [matTooltip]="getInstrumentNames(instructor.instructorDetail.instruments)">
                {{ instructor.instructorDetail.instruments.length - 1 }}+
              </div>
            }
          } @else {
            -
          }
        </div>
      </div>
    </div>

    @if ($index < instructors.length - 1) {
      <div class="dotted-divider"></div>
    }
  }
</ng-template>

<ng-template #noDataFound>
  <div class="no-data-found-wrapper">
    <h3>NO DATA FOUND!</h3>
  </div>
</ng-template>

<ng-template #showLoader>
  <div class="page-loader-wrapper">
    <app-content-loader></app-content-loader>
  </div>
</ng-template>
