import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { takeUntil } from 'rxjs';
import { AuthService } from 'src/app/auth/services';
import { SchoolLocations } from 'src/app/pages/room-and-location-management/models';
import { Instrument } from 'src/app/request-information/models';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { CBResponse } from 'src/app/shared/models';
import { AppToasterService, CommonService } from 'src/app/shared/services';
import { SharedModule } from 'src/app/shared/shared.module';
import { CardDetailsResponse, PaymentParams, SelectedLessonInfo } from '../../models';
import { ClassTypes, LessonTypes } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/models';
import { SchedulerService } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/services';
import { PaymentService } from 'src/app/pages/schedule-classes/services';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { MatTooltipModule } from '@angular/material/tooltip';
import { AssignedInstructors, AssignedInstruments } from '../../../ensemble-class/models';
import { PaymentMethodsComponent } from '../../../../../../shared/components/payment-methods/payment-methods.component';
import { MatFormFieldModule } from '@angular/material/form-field';
import { FormsModule } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { TransactionTypes } from 'src/app/pages/shop/models';
import { InstrumentsService } from 'src/app/request-information/services';
import { LocalDatePipe } from 'src/app/shared/pipe';
import { CommonUtils } from 'src/app/shared/utils';

const DEPENDENCIES = {
  MODULES: [CommonModule, MatButtonModule, SharedModule, MatTooltipModule, MatFormFieldModule, FormsModule, MatInputModule, MatIconModule],
  COMPONENTS: [PaymentMethodsComponent],
  PIPES: [LocalDatePipe]
};

@Component({
  selector: 'app-book-introductory-lesson',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.COMPONENTS, ...DEPENDENCIES.PIPES],
  templateUrl: './book-introductory-lesson.component.html',
  styleUrl: './book-introductory-lesson.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class BookIntroductoryLessonComponent extends BaseComponent implements OnInit {
  @Input() scheduleInfo!: SelectedLessonInfo;
  @Input() isScheduleMakeUpLesson!: boolean;
  @Input() selectedDependentId!: number | undefined;

  instruments!: Array<Instrument>;
  locations!: Array<SchoolLocations>;
  cardDetails!: CardDetailsResponse;
  isPaymentDone = false;
  isRePayment = false;
  classTypes = ClassTypes;
  lessonTypes = LessonTypes;
  discountAmount: number = 0;
  discountedPrice: number = 0;
  showDiscountField: boolean = false;
  discountError: string | null = null;
  totalPayableAmount: number = 0;
  isAddressIncomplete = false;

  @Output() closeEnrollmentSideNav = new EventEmitter<void>();
  @Output() confirmAppointments = new EventEmitter<void>();
  @Output() confirmAppointmentsGroup = new EventEmitter<void>();
  @Output() scheduleIntroductoryLesson = new EventEmitter<any>();
  @Output() rePaymentForTheSchedule = new EventEmitter<any>();

  constructor(
    private readonly commonService: CommonService,
    private readonly cdr: ChangeDetectorRef,
    private readonly authService: AuthService,
    protected readonly schedulerService: SchedulerService,
    private readonly toasterService: AppToasterService,
    private readonly instrumentsService: InstrumentsService,
    private readonly paymentService: PaymentService
  ) {
    super();
  }

  ngOnInit(): void {
    this.getCurrentUserDetails();
    this.getLocations();
    this.getCardDetailsForPaymentInitiate();
  }

  onDiscountChange(): void {
    const maxPrice = this.scheduleInfo.price ?? this.constants.defaultAmountPayableForIntroductoryClasses;

    this.discountError = null;

    if (this.discountAmount < 0) {
      this.discountAmount = 0;
      this.discountError = 'Discount cannot be negative';
    } else if (this.discountAmount > maxPrice) {
      this.discountAmount = maxPrice;
      this.discountError = `Discount cannot exceed $${maxPrice.toFixed(2)}`;
    }

    this.calculateTotalPayableAmount();
  }

  calculateTotalPayableAmount(): void {
    const originalPrice = this.scheduleInfo.price ?? this.constants.defaultAmountPayableForIntroductoryClasses;
    this.discountedPrice = originalPrice - this.discountAmount;
    this.totalPayableAmount = this.discountedPrice;
  }

  clearDiscount(): void {
    this.showDiscountField = false;
    this.discountAmount = 0;
    this.discountError = null;
    this.calculateTotalPayableAmount();
  }

  getCardDetailsForPaymentInitiate(): void {
    this.paymentService.userCardDetails$.pipe(takeUntil(this.destroy$)).subscribe(card => {
      if (card) {
        this.cardDetails = card;
        this.onPaymentConfirmation(card);
      }
    });
  }

  getCurrentUserDetails(): void {
    this.showPageLoader = true;
    const userId = this.scheduleInfo.studentDetails ? this.scheduleInfo.studentDetails![0].accountManagerId : undefined;
    this.authService
      .getUserDetailsFromId(userId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: res => {
          this.currentUser = res.result;
          if (this.currentUser?.summerCampDiscountPercentage && this.scheduleInfo.classType === ClassTypes.SUMMER_CAMP) {
            this.discountAmount = (this.scheduleInfo.price ?? this.constants.defaultAmountPayableForIntroductoryClasses) * (this.currentUser.summerCampDiscountPercentage / 100);
          }
          this.calculateTotalPayableAmount();
          this.getInstruments();
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  getInstruments(): void {
    const isHiddenFromClient = this.currentUser?.userRoleId === this.constants.roleIds.CLIENT;
    this.instrumentsService
      .getList<CBResponse<Instrument>>(`${API_URL.crud.getAll}?IsHiddenFromClient=${isHiddenFromClient}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<Instrument>) => {
          this.instruments = res.result.items;
          this.cdr.detectChanges();
        }
      });
  }

  getLocations(): void {
    this.commonService
      .getLocations()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<SchoolLocations>) => {
          this.locations = res.result.items;
          this.cdr.detectChanges();
        }
      });
  }

  onScheduleMakeUpClass(): void {
    this.confirmAppointments.emit();
  }

  onPaymentDone(): void {
    this.showBtnLoader = true;
    setTimeout(() => {
      //tobe use
      // if (this.cardConnectPaymentComponent.isCardInvalid) {
      //   this.toasterService.error(this.constants.errorMessages.invalidCardDetails);
      //   // this.cardConnectPaymentComponent.resetTokenFrame();
      // } else {
      //   // this.cardConnectPaymentComponent.tokenize();
      //   this.cardDetails = this.cardConnectPaymentComponent.cardDetails;
      //   this.isPaymentDone = true;
      //   this.cdr.detectChanges();
      // }
      this.showBtnLoader = false;
      this.cdr.detectChanges();
    }, 1000);
  }

  onCloseModal(): void {
    this.showBtnLoader = false;
    this.closeEnrollmentSideNav.emit();
  }

  isAddressIncompleteFn(): void {
    this.paymentService.isAddressIncomplete$.pipe(takeUntil(this.destroy$)).subscribe(isIncomplete => {
       this.isAddressIncomplete = isIncomplete;
       this.cdr.markForCheck();
    });
  }

  async onPaymentConfirmation(card: CardDetailsResponse): Promise<void> {
    card.paidAmount = this.totalPayableAmount;
    card.discountedAmount = this.discountAmount;
    card.transactionType = TransactionTypes.CARD;
    card.totalAmount = this.scheduleInfo.price ?? this.constants.defaultAmountPayableForIntroductoryClasses;
    if (this.isRePayment && this.scheduleInfo.classType === ClassTypes.INTRODUCTORY) {
      this.rePaymentForTheSchedule.emit(card);
      return;
    }

    if (this.scheduleInfo.classType === ClassTypes.INTRODUCTORY) {
      this.scheduleIntroductoryLesson.emit(card);
      return;
    }
    this.initPaymentForGroupAndSummerCamp();
  }

  initPaymentForGroupAndSummerCamp(): void {
    this.isAddressIncompleteFn();
    if (this.isAddressIncomplete) {
     this.toasterService.error(this.constants.errorMessages.addressIncomplete);
     return;
    }
    this.paymentService.showBtnLoader(true);
    if (this.cardDetails.isUsingSavedCard === false) {
      this.paymentService.add(this.getPaymentParams(), API_URL.payment.NMIPayment).subscribe({
        next: res => {
          this.isPaymentDone = true;
          this.toasterService.success(this.constants.successMessages.paymentSuccess);
          this.confirmAppointmentsGroup.emit();
          this.isRePayment = false;
          this.paymentService.showBtnLoader(false);
          this.cdr.detectChanges();
        },
        error: () => {
          this.isPaymentDone = false;
          this.isRePayment = true;
          this.paymentService.showBtnLoader(false);
          this.cdr.detectChanges();
        }
      });
    } else {
      this.paymentService
        .add(this.getCardDetails(), API_URL.payment.paymentUsingSavedCard)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: () => {
            this.isPaymentDone = true;
            this.toasterService.success(this.constants.successMessages.paymentSuccess);
            this.confirmAppointmentsGroup.emit();
            this.isRePayment = false;
            this.paymentService.showBtnLoader(false);
            this.cdr.detectChanges();
          },
          error: () => {
            this.isPaymentDone = false;
            this.isRePayment = true;
            this.paymentService.showBtnLoader(false);
            this.cdr.detectChanges();
          }
        });
    }
  }
  
  getCardDetails(): PaymentParams {
    return {
      userId: this.currentUser?.userId,
      dependentInformationId: this.selectedDependentId ?? this.currentUser?.dependentId,
      classType: this.scheduleInfo.classType,
      scheduleId: this.scheduleInfo.id,
      amount: this.scheduleInfo.price,
      paidDate: new Date(),
      customerVaultId: this.cardDetails.customerVaultId,
      transactionType: TransactionTypes.CARD,
      paidAmount: this.totalPayableAmount,
      totalAmount: this.scheduleInfo.price,
      discountedAmount: this.discountAmount
    };
  }

  getPaymentParams(): PaymentParams {
    return {
      dependentInformationId: this.selectedDependentId ?? this.currentUser?.dependentId,
      classType: this.scheduleInfo.classType,
      scheduleId: this.scheduleInfo.id,
      amount: this.scheduleInfo.price,
      ccNum: this.cardDetails.number,
      ccExpiry: this.cardDetails.expiry,
      ccType: this.cardDetails.type,
      token: this.cardDetails.token,
      isSaveCard: this.cardDetails.isSaveCard,
      paidDate: new Date(),
      address: this.cardDetails.address,
      city: this.cardDetails.city,
      state: this.cardDetails.state,
      zip: this.cardDetails.zip,
      firstName: this.cardDetails.firstName,
      lastName: this.cardDetails.lastName,
      transactionType: TransactionTypes.CARD,
      paidAmount: this.totalPayableAmount,
      totalAmount: this.scheduleInfo.price,
      discountedAmount: this.discountAmount
    };
  }

  setIsPaymentDone(isPaymentDone: boolean): void {
    this.isPaymentDone = isPaymentDone;
    this.cdr.detectChanges();
  }

  setIsRePaymentDone(isRePayment: boolean): void {
    this.isRePayment = isRePayment;
    this.cdr.detectChanges();
  }

  getInstrumentNames(array: AssignedInstruments[]): string {
    return array
      .slice(1)
      .map(item => item.instrumentName)
      .join(', ');
  }

  getInstructorNames(array: AssignedInstructors[]): string {
    return array
      .slice(1)
      .map(item => item.instructorName)
      .join(', ');
  }

  getInstrumentNameFromValue(value: number | undefined): string {
    return this.instruments?.find(name => name.instrumentDetail.id === value)?.instrumentDetail.name || '';
  }

  getLocationNameFromValue(value: number | undefined): string {
    return this.locations?.find(name => name.schoolLocations.id === value)?.schoolLocations.locationName || '';
  }

  getDependentNameFromValue(value: number | undefined): string {
    return CommonUtils.getDependentNameFromValue(value, this.currentUser);
  }

  preventInvalidInput(event: KeyboardEvent): void {
    // Allow: backspace, delete, tab, escape, enter, decimal point
    const allowedKeys = ['Backspace', 'Delete', 'Tab', 'Enter', 'Escape', 'ArrowLeft', 'ArrowRight', '.'];

    // Allow: Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X
    if ((event.ctrlKey && ['a', 'c', 'v', 'x'].includes(event.key.toLowerCase())) || allowedKeys.includes(event.key)) {
      return;
    }

    // Allow numbers
    if (/^\d$/.test(event.key)) {
      const input = event.target as HTMLInputElement;
      const value = input.value;
      const selectionStart = input.selectionStart || 0;
      const selectionEnd = input.selectionEnd || 0;

      // Calculate what the new value would be
      const newValue = value.substring(0, selectionStart) + event.key + value.substring(selectionEnd);
      const numericValue = parseFloat(newValue);
      const maxPrice = this.scheduleInfo.price ?? this.constants.defaultAmountPayableForIntroductoryClasses;

      // If the new value would exceed the max price, prevent the input
      if (!isNaN(numericValue) && numericValue > maxPrice) {
        event.preventDefault();
      }
    } else {
      // Not a number, prevent input
      event.preventDefault();
    }
  }
}
