import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { SharedModule } from 'src/app/shared/shared.module';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { CommonModule, DatePipe } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { AvailabilityType, InstructorDetails, InstructorInformationParams, InstructorNotes } from '../../models';
import { CommonUtils } from 'src/app/shared/utils';
import { MatIconModule } from '@angular/material/icon';
import { takeUntil } from 'rxjs';
import { CBGetResponse, CBResponse, MatDialogRes } from 'src/app/shared/models';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { DashIfEmptyPipe, LocalDatePipe } from 'src/app/shared/pipe';
import moment, { Moment } from 'moment';
import { SchedulerService } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/services';
import {
  AdvancedFilters,
  ClassTypes,
  ScheduleDetail,
  ScheduleDetailsView,
  ScheduleFilters
} from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/models';
import { Debounce } from 'src/app/shared/decorators';
import { MatSidenavModule } from '@angular/material/sidenav';
import { InstructorUnderSupervisorComponent } from '../../../supervisors/pages/instructor-under-supervisor/instructor-under-supervisor.component';
import { EChartsOption } from 'echarts';
import { LeaveRequestService } from 'src/app/pages/requests/pages/leave-request/services';
import { LeaveBalance } from 'src/app/pages/requests/pages/leave-request/models';
import { ChatService } from 'src/app/pages/messages/services';
import { ChatHistoryRes, ChatMessageType, FileType } from 'src/app/pages/messages/models';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MessagesComponent } from '../../../../../messages/messages.component';
import { IdEmailModel } from '../../../students/models';
import { DateUtils } from 'src/app/shared/utils/date.utils';
import { Account } from 'src/app/auth/models/user.model';
import { MatDialog } from '@angular/material/dialog';
import { ConfirmationDialogComponent } from 'src/app/shared/components/confirmation-dialog/confirmation-dialog.component';
import { InstructorService } from 'src/app/schedule-introductory-lesson/services';
import { AppToasterService, NavigationService } from 'src/app/shared/services';
import { MatFormFieldModule } from '@angular/material/form-field';
import { provideNativeDateAdapter } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { FormsModule } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { WorkingHours } from 'src/app/pages/dashboard/models';
import { Router } from '@angular/router';
import { MembersNotesComponent } from '../../../shared/members-notes/members-notes.component';
import { MonthYearPickerComponent } from '../../../shared/month-year-picker/month-year-picker.component';
 
const DEPENDENCIES = {
  MODULES: [
    CommonModule,
    MatButtonModule,
    SharedModule,
    MatIconModule,
    MatIconModule,
    MatSidenavModule,
    MatTooltipModule,
    MatFormFieldModule,
    MatDatepickerModule,
    FormsModule,
    MatInputModule
  ],
  PIPES: [DashIfEmptyPipe, LocalDatePipe],
  COMPONENTS: [InstructorUnderSupervisorComponent, MessagesComponent, MembersNotesComponent, MonthYearPickerComponent]
};
@Component({
  selector: 'app-view-instructor',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.PIPES, ...DEPENDENCIES.COMPONENTS],
  providers: [provideNativeDateAdapter()],
  templateUrl: './view-instructor.component.html',
  styleUrl: './view-instructor.component.scss'
})
export class ViewInstructorComponent extends BaseComponent implements OnChanges, OnInit {
  @Input() selectedInstructorViewDetails!: InstructorDetails | null;
  @Input() isSupervisor!: boolean;
  @Input() isFromScheduler!: boolean;
  @Input() currentUser$!: Account | null;
 
  showScheduleLoader = false;
  isBioCollapsed = true;
  isLocationCollapsed = true;
  isSideNavOpen = false;
  isMessageSideNavOpen = false;
  isEditLeaveBalance = false;
  showNotesLoader = false;
  currentPage = this.paginationConfig.pageNumber;
  pageSize = this.paginationConfig.twoItemsPerPage;
  chatTypes = ChatMessageType;
  fileTypes = FileType;
  chartOptions!: EChartsOption;
  workingHoursChart!: EChartsOption;
  workingHours: WorkingHours = {
    totalWorkingHours: 0,
    totalActualHours: 0
  };
  usedLeaves!: number;
  availableLeaves!: number;
  totalLeaves!: number;
  leaveBalance!: LeaveBalance[];
  chatHistory!: Array<ChatHistoryRes>;
  noIntroductoryInstrumentAvailable?: boolean;
  classTypes = ClassTypes;
  filters: InstructorInformationParams = {
    startDate: '',
    endDate: ''
  };
  appliedAdvanceFilter = new AdvancedFilters();
  events!: Array<ScheduleDetailsView>;
  selectedIdEmail!: IdEmailModel | null;
  selectedYear = moment();
 
  @Output() closeViewSideNav = new EventEmitter<void>();
  @Output() openEditSideNav = new EventEmitter<void>();
  @Output() refreshInstructors = new EventEmitter<void>();
 
  constructor(
    private readonly cdr: ChangeDetectorRef,
    private readonly datePipe: DatePipe,
    private readonly schedulerService: SchedulerService,
    private readonly leaveRequestService: LeaveRequestService,
    private readonly instructorService: InstructorService,
    private readonly chatService: ChatService,
    private readonly dialog: MatDialog,
    private readonly toasterService: AppToasterService,
    private readonly navigationService: NavigationService,
    private readonly router: Router
  ) {
    super();
  }
 
  ngOnInit(): void {
    this.selectedYear = moment();
  }
 
  ngOnChanges(changes: SimpleChanges): void {
    this.showPageLoader = true;
    if (changes['selectedInstructorViewDetails']?.currentValue) {
      this.selectedInstructorViewDetails = changes['selectedInstructorViewDetails'].currentValue;
      if (this.selectedInstructorViewDetails?.instructorAvailability?.length) {
        this.selectedInstructorViewDetails.instructorAvailability = this.selectedInstructorViewDetails?.instructorAvailability.map(
          item => ({
            ...item,
            availableStartTime: DateUtils.toLocal(item.availableStartTime, 'yyyy-MM-DDTHH:mm:ss'),
            availableEndTime: DateUtils.toLocal(item.availableEndTime, 'yyyy-MM-DDTHH:mm:ss'),
            availableStartDate: DateUtils.toLocal(item.availableStartDate, 'yyyy-MM-DDTHH:mm:ss'),
            availableEndDate: DateUtils.toLocal(item.availableEndDate, 'yyyy-MM-DDTHH:mm:ss')
          })
        );
      }
      this.loadMessages();
      this.resetFiltersToToday();
      console.log(this.filters.startDate, this.filters.endDate);
      this.getDependentSchedule(this.filters.startDate, this.filters.endDate);
      this.getIntroductoryInstrumentAvailability();
      this.getLeaveBalance(this.selectedInstructorViewDetails?.email!);
      this.getInstructorWorkingHours();
    }
  }
 
  resetFiltersToToday(): void {
    this.filters.startDate = DateUtils.getUtcRangeForLocalDate(new Date()).startUtc;
    this.filters.endDate = DateUtils.getUtcRangeForLocalDate(new Date()).endUtc;
    console.log(this.filters.startDate, this.filters.endDate);
  }
 
  loadMessages(): void {
    this.chatService
      .add(
        {
          page: this.currentPage,
          pageSize: this.pageSize,
          userEmail: this.selectedInstructorViewDetails?.email
        },
        API_URL.octopusChatAppServices.chatHistory
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<ChatHistoryRes>) => {
          this.chatHistory = res.result.items.map((message: ChatHistoryRes) => ({
            ...message,
            localLatestMessageTime: DateUtils.toLocal(message.lastMessageTime, 'yyyy-MM-DDTHH:mm:ss.SSS+0000', 'yyyy-MM-DDTHH:mm:ss')
          }));
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }
 
  @Debounce(300)
  getDependentSchedule(minDate: string, maxDate: string): void {
    this.showScheduleLoader = true;
    this.schedulerService
      .add(this.getFilterParams(minDate, maxDate), API_URL.crud.getAll)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<ScheduleDetail>) => {
          this.events = res.result.scheduleLessonDetails;
          this.showPageLoader = false;
          this.showScheduleLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.showScheduleLoader = false;
          this.cdr.detectChanges();
        }
      });
  }
 
  getFilterParams(minDate: string, maxDate: string): ScheduleFilters {
    console.log(minDate, maxDate);
    const minScheduleDateFilter = DateUtils.getUtcRangeForLocalDate(minDate).startUtc;
    const maxScheduleDateFilter = DateUtils.getUtcRangeForLocalDate(maxDate).endUtc;
    console.log(minScheduleDateFilter, maxScheduleDateFilter);
 
    return {
      minScheduleDateFilter: minScheduleDateFilter,
      maxScheduleDateFilter: maxScheduleDateFilter,
      locationIdFilter: [],
      instructorIdFilter: [this.selectedInstructorViewDetails?.id!],
      classTypeFilter: [],
      instrumentIdFilter: [],
      isNotShowDraftSchedule:
        this.currentUser$?.userRoleId === this.constants.roleIds.INSTRUCTOR ||
        this.currentUser$?.userRoleId === this.constants.roleIds.SUPERVISOR
          ? true
          : false,
      ...this.appliedAdvanceFilter
    };
  }
 
  getTimeDiff(start: string, end: string): number | null {
    return CommonUtils.calculateTimeDifference(new Date(start), new Date(end));
  }
 
  toggleMessageSideNav(isOpen: boolean): void {
    this.isMessageSideNavOpen = isOpen;
    this.selectedIdEmail = isOpen ? { email: this.selectedInstructorViewDetails?.email } : null;
  }

  getIntroductoryInstrumentAvailability(): void {
    this.noIntroductoryInstrumentAvailable = this.selectedInstructorViewDetails?.instruments?.every(
      instrument => !instrument.isIntroductoryClassAvailable
    );
  }

  updateDay(daysToAdd: number): void {
    const date = moment(this.filters.endDate).add(daysToAdd, 'days');
    const utcRange = DateUtils.getUtcRangeForLocalDate(date.toDate());

    this.filters.startDate = utcRange.startUtc;
    this.filters.endDate = utcRange.endUtc;
    console.log(this.filters.startDate, this.filters.endDate);
    this.getDependentSchedule(this.filters.startDate, this.filters.endDate);
  }

  onDateChange(): void {
    const utcRange = DateUtils.getUtcRangeForLocalDate(this.filters.startDate);
    this.filters.startDate = utcRange.startUtc;
    this.filters.endDate = utcRange.endUtc;
    console.log(this.filters.startDate, this.filters.endDate);
    this.getDependentSchedule(this.filters.startDate, this.filters.endDate);
  }

  formatHoursToHM(decimalHours: number): string {
    return CommonUtils.formatHoursToHM(decimalHours);
  }

  getInstructorWorkingHours(selectedYear = moment()): void {
    const startDate = DateUtils.getUtcRangeForLocalDate(selectedYear.startOf('month').toDate()).startUtc;
    const endDate = DateUtils.getUtcRangeForLocalDate(selectedYear.endOf('month').toDate()).endUtc;

    this.instructorService
      .getList<CBGetResponse<WorkingHours>>(
        `${API_URL.instructorDetails.getInstructorMonthlyHours}?instructorId=${this.selectedInstructorViewDetails?.id}&startDate=${startDate}&endDate=${endDate}`
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<WorkingHours>) => {
          this.workingHours = res.result;
          this.workingHours.pendingWorkingHours = this.workingHours.totalWorkingHours - this.workingHours.totalActualHours;
          this.workingHoursChart = CommonUtils.getWorkingHoursChartOptions(
            this.workingHours.totalActualHours,
            this.workingHours.pendingWorkingHours,
            this.workingHours.totalWorkingHours
          );
          this.cdr.detectChanges();
        }
      });
  }

  getAvailableDays(availableDays: number[]): string {
    const daysOfWeek = this.constants.daysOfTheWeek;
    return availableDays.map(day => daysOfWeek[day].label).join(', ');
  }

  onEdit(): void {
    this.openEditSideNav.emit();
  }

  openStudentDetails(studentId: number): void {
    this.navigationService.navigateToStudentDetail(studentId);
  }

  onDeleteConfirmation(): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: `Delete Staff Member`,
        message: `Are you sure you want to delete this staff member?`
      }
    });

    dialogRef.afterClosed().subscribe((result: MatDialogRes) => {
      if (result.isConfirmed) {
        this.onDeleteStaff(this.selectedInstructorViewDetails?.id);
      }
    });
  }

  onDeleteStaff(instructorId: number | undefined): void {
    if (!instructorId) return;
    this.showBtnLoader = true;
    this.instructorService.delete(instructorId, API_URL.crud.delete).subscribe({
      next: () => {
        this.showBtnLoader = false;
        this.refreshInstructors.emit();
        this.closeViewSideNavFun();
        this.toasterService.success(this.constants.successMessages.deletedSuccessfully.replace('{item}', 'Staff Member'));
        this.cdr.detectChanges();
      },
      error: () => {
        this.showBtnLoader = false;
        this.cdr.detectChanges();
      }
    });
  }

  getInitials(name?: string): string {
    return CommonUtils.getInitialsUsingFullName(name);
  }

  getAvailabilityTypeName(value: number): string {
    return AvailabilityType[value];
  }

  getLeaveBalance(email: string): void {
    this.leaveRequestService
      .getList<CBGetResponse<LeaveBalance[]>>(`${API_URL.leaveManagement.getLeaveBalance}?UserEmail=${email}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<LeaveBalance[]>) => {
          this.leaveBalance = res.result;
          this.totalLeaves = this.leaveBalance[0].totalLeaveDays;
          this.usedLeaves = this.leaveBalance[0].usedLeaveDays;
          this.availableLeaves = this.leaveBalance[0].remainingLeaveDays;
          this.chartOptions = CommonUtils.getChartOptions(this.availableLeaves, this.usedLeaves, this.totalLeaves);
          this.cdr.detectChanges();
        }
      });
  }

  toggleLocationCollapse(): void {
    this.isLocationCollapsed = !this.isLocationCollapsed;
  }

  closeViewSideNavFun(): void {
    this.router.navigate([], { queryParams: {} });
    this.isLocationCollapsed = true;
    this.isBioCollapsed = true;
    this.selectedYear = moment();
    this.closeViewSideNav.emit();
  }
}
