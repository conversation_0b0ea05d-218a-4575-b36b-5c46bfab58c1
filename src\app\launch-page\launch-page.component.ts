import { Component, OnInit, OnDestroy, ChangeDetectorRef } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { BaseComponent } from '../shared/components/base-component/base.component';
import { MatTooltipModule } from '@angular/material/tooltip';
import { Router } from '@angular/router';
import { ROUTER_PATHS } from '../shared/constants';
import { DirectivesModule } from '../shared/directives/directives.module';
import { CommonModule } from '@angular/common';

const DEPENDENCIES = {
  MODULES: [MatButtonModule, MatTooltipModule, DirectivesModule, CommonModule]
};

@Component({
  selector: 'app-launch-page',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES],
  templateUrl: './launch-page.component.html',
  styleUrl: './launch-page.component.scss'
})
export class LaunchPageComponent extends BaseComponent implements OnInit, On<PERSON><PERSON>roy {
  days = '00';
  hours = '00';
  minutes = '00';
  seconds = '00';
  targetDate!: Date;
  isCountdownComplete = false;

  constructor(private cdr: ChangeDetectorRef, private readonly router: Router) {
    super();
  }

  ngOnInit() {
    this.startCountdown();
  }

  startCountdown() {
    this.targetDate = new Date('2026-01-16T10:00:00');
    this.updateCountdown();
    setInterval(() => {
      this.updateCountdown();
    }, 1000);
  }

  updateCountdown() {
    const now = new Date().getTime();
    const targetTime = this.targetDate.getTime();
    const difference = targetTime - now;

    if (difference > 0) {
      const days = Math.floor(difference / (1000 * 60 * 60 * 24));
      const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((difference % (1000 * 60)) / 1000);

      this.days = this.padZero(days);
      this.hours = this.padZero(hours);
      this.minutes = this.padZero(minutes);
      this.seconds = this.padZero(seconds);

      this.cdr.detectChanges();
    } else {
      this.days = '00';
      this.hours = '00';
      this.minutes = '00';
      this.seconds = '00';
      this.isCountdownComplete = true;

      this.cdr.detectChanges();
    }
  }

  padZero(num: number): string {
    return num < 10 ? '0' + num : num.toString();
  }

  onResetPassword(): void {
    this.router.navigate(
      [ROUTER_PATHS.auth.root, ROUTER_PATHS.auth.forgotPassword.root, ROUTER_PATHS.auth.forgotPassword.init],
      {
        queryParams: { origin: 'launch-page' }
      }
    );
  }

  onScheduleIntroductoryLesson(): void {
    this.router.navigate([ROUTER_PATHS.scheduleIntroductoryLesson], {
      queryParams: { origin: 'launch-page' }
    });
  }

  onSignUp(): void {
    this.router.navigate([ROUTER_PATHS.auth.root, ROUTER_PATHS.auth.signUp]);
  }

  onSignIn(): void {
    this.router.navigate([ROUTER_PATHS.auth.root, ROUTER_PATHS.auth.login]);
  }
}
