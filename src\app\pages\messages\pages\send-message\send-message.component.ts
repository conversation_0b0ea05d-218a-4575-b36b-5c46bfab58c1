import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSidenavModule } from '@angular/material/sidenav';
import { takeUntil } from 'rxjs';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { AppToasterService } from 'src/app/shared/services';
import { SharedModule } from 'src/app/shared/shared.module';
import { CommonUtils } from 'src/app/shared/utils';
import { MatSelectModule } from '@angular/material/select';
import { MultiSelectChipsComponent } from 'src/app/shared/components/multi-select-chips/multi-select-chips.component';
import { CBGetResponse, CBResponse, FileUpload, IdNameModel } from 'src/app/shared/models';
import { ChatMessageType, FileType, ReceieverId, SendMessageFilter, SendMessageFormGroup, UserAccordngToRole } from '../../models';
import { MatIconModule } from '@angular/material/icon';
import { ChatService, RoleUserService } from '../../services';
import { EnumToKeyValuePipe } from 'src/app/shared/pipe';
import { Students } from 'src/app/pages/members/pages/students/models';
import { DependentService } from 'src/app/pages/profile/services';
import { UploadFileService } from 'src/app/shared/services/upload-file.service';
import { Account } from 'src/app/auth/models/user.model';

const DEPENDENCIES = {
  MODULES: [
    SharedModule,
    MatSidenavModule,
    MatButtonModule,
    CommonModule,
    MatFormFieldModule,
    MatInputModule,
    ReactiveFormsModule,
    MatSelectModule,
    MatIconModule,
    FormsModule
  ],
  COMPONENTS: [MultiSelectChipsComponent],
  PIPES: [EnumToKeyValuePipe]
};

@Component({
  selector: 'app-send-message',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.COMPONENTS, ...DEPENDENCIES.PIPES],
  templateUrl: './send-message.component.html',
  styleUrl: './send-message.component.scss'
})
export class SendMessageComponent extends BaseComponent implements OnInit {
  @Input() currentUser$!: Account | null;

  sendMessageForm!: FormGroup<SendMessageFormGroup>;
  allUsers!: Array<UserAccordngToRole>;
  chatMessageType = ChatMessageType;
  isAttachmentMenuOpen = false;
  fileUploaded = false;
  isFileUploading = false;
  fileName!: string;
  fileExtension!: string;
  fileTypes = FileType;
  fullPath!: string;
  filters: SendMessageFilter = {
    admin: {
      id: 1,
      defaultPlaceholder: 'Admin',
      placeholder: 'Admin',
      value: [] as Array<IdNameModel>,
      totalCount: 0,
      showMax: 1,
      isOpen: false,
      showSearchBar: true,
      options: [] as Array<IdNameModel>
    },
    deskManager: {
      id: 2,
      defaultPlaceholder: 'Desk Manager',
      placeholder: 'Desk Manager',
      value: [] as Array<IdNameModel>,
      totalCount: 0,
      showMax: 1,
      isOpen: false,
      showSearchBar: true,
      options: [] as Array<IdNameModel>
    },
    instructor: {
      id: 3,
      defaultPlaceholder: 'Instructor',
      placeholder: 'Instructor',
      value: [] as Array<IdNameModel>,
      totalCount: 0,
      showMax: 1,
      isOpen: false,
      showSearchBar: true,
      options: [] as Array<IdNameModel>
    },
    supervisor: {
      id: 4,
      defaultPlaceholder: 'Supervisor',
      placeholder: 'Supervisor',
      value: [] as Array<IdNameModel>,
      totalCount: 0,
      showMax: 1,
      isOpen: false,
      showSearchBar: true,
      options: [] as Array<IdNameModel>
    },
    client: {
      id: 5,
      defaultPlaceholder: 'Client',
      placeholder: 'Client',
      value: [] as Array<IdNameModel>,
      totalCount: 0,
      showMax: 1,
      isOpen: false,
      showSearchBar: true,
      options: [] as Array<IdNameModel>
    }
  };

  @Output() refreshData = new EventEmitter<void>();
  @Output() closeSideNav = new EventEmitter<void>();

  constructor(
    private readonly toasterService: AppToasterService,
    private readonly cdr: ChangeDetectorRef,
    private readonly roleUserService: RoleUserService,
    private readonly chatService: ChatService,
    private readonly dependentService: DependentService,
    private readonly uploadFileService: UploadFileService
  ) {
    super();
  }

  ngOnInit(): void {
    this.initSendMessageForm();
    this.getUsersAccordingToRole();
    this.getStudentList();
  }

  initSendMessageForm(): void {
    this.sendMessageForm = new FormGroup<SendMessageFormGroup>({
      message: new FormControl(null, { nonNullable: true }),
      chatType: new FormControl(ChatMessageType.PRIVATE, { nonNullable: true, validators: [Validators.required] }),
      receivers: new FormControl([], {
        nonNullable: true,
        validators: [Validators.required]
      }),
      fileDetails: new FormControl(null, { nonNullable: true })
    });
  }

  getValidFilterCount(): number {
    return Object.values(this.filters)
      .map(filter => filter.value.length)
      .reduce((total, length) => total + length, 0);
  }

  toggleAttachmentMenu(): void {
    this.isAttachmentMenuOpen = !this.isAttachmentMenuOpen;
  }

  onFileSelected(event: Event): void {
    this.isAttachmentMenuOpen = false;
    this.fileUploaded = false;

    const input = event.target as HTMLInputElement;
    if (!input.files?.length) return;

    const file = input.files[0];
    const fileExtension = file.name.split('.').pop() || '';

    switch (true) {
      case file.size > this.constants.maxFileSizes.MAX_FILE_SIZE_2MB && this.getFileType(fileExtension) === FileType.IMAGE:
        this.toasterService.error(this.constants.errorMessages.fileSizeExceeds.replace('{item}', 'Image').replace('{size}', '2MB'));
        this.fileUploaded = true;
        break;
      case file.size > this.constants.maxFileSizes.MAX_FILE_SIZE_10MB && this.getFileType(fileExtension) === FileType.VIDEO:
        this.toasterService.error(this.constants.errorMessages.fileSizeExceeds.replace('{item}', 'Video').replace('{size}', '10MB'));
        this.fileUploaded = true;
        break;
      case file.size > this.constants.maxFileSizes.MAX_FILE_SIZE_5MB && this.getFileType(fileExtension) === FileType.DOCUMENT:
        this.toasterService.error(this.constants.errorMessages.fileSizeExceeds.replace('{item}', 'Document').replace('{size}', '5MB'));
        this.fileUploaded = true;
        break;
      default:
        this.fileName = file.name;
        this.fileExtension = fileExtension;
        this.uploadFileToAws(file);
    }

    input.value = '';
  }

  uploadFileToAws(params: File): void {
    this.isFileUploading = true;
    this.uploadFileService
      .uploadFile(API_URL.uploadFile.uploadFileToS3, this.getDocumentParams(params))
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<FileUpload>) => {
          if (res.result?.fullPath && res.result.shortPath) {
            this.fullPath = res.result.fullPath;
            this.sendMessageForm.controls.fileDetails?.setValue({
              fileName: this.fileName,
              filePath: res.result.shortPath,
              fileType: this.getFileType(),
              fileExtension: this.fileExtension
            });
          }
          this.fileUploaded = true;
          this.isFileUploading = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.fileUploaded = false;
          this.isFileUploading = false;
          this.cdr.detectChanges();
        }
      });
  }

  getFileType(fileExtension?: string): number {
    const fileExt = fileExtension ?? this.fileExtension;

    if (!fileExt) return 0;
    const documentTypes = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'txt'];
    const videoTypes = ['mp4', 'mov', 'avi', 'mkv', 'webm', 'flv', 'wmv'];

    if (documentTypes.some(type => fileExt.includes(type))) {
      return FileType.DOCUMENT;
    } else if (videoTypes.some(type => fileExt.includes(type))) {
      return FileType.VIDEO;
    } else {
      return FileType.IMAGE;
    }
  }

  getDocumentParams(selectedFile: File): FormData {
    const formData = new FormData();
    formData.append('file', selectedFile, selectedFile.name);
    formData.append('uploadType', '4');
    return formData;
  }

  getReceiversList(): Array<ReceieverId> {
    return Object.values(this.filters).flatMap(filter => {
      return filter.value.map((item: IdNameModel) => {
        if (filter === this.filters.client) {
          return {
            receiverId: item.accountManagerId,
            studentId: item.id
          };
        } else {
          return {
            receiverId: item.id
          };
        }
      });
    });
  }

  checksBeforeSendingMessage(): void {
    this.setFormControlValue('receivers', this.getReceiversList());
    this.setRequiredBasedOnCondition('message', !this.sendMessageForm.controls.fileDetails?.value);
  }

  checkIfPhoneNumberOrEmailIncluded(): boolean {
    const messageValue = this.sendMessageForm.getRawValue().message!;
    const { INSTRUCTOR, SUPERVISOR, CLIENT } = this.constants.roleIds;

    if (
      [INSTRUCTOR, SUPERVISOR, CLIENT].includes(this.currentUser$?.userRoleId!) &&
      (this.constants.pattern.EMAIL.test(messageValue) || this.constants.pattern.PHONE_NUMBER_PATTERN.test(messageValue))
    ) {
      this.toasterService.error(this.constants.errorMessages.sensitiveInfo);
      return true;
    }

    return false;
  }

  onSendMessage(): void {
    this.checksBeforeSendingMessage();
    if (this.sendMessageForm.invalid || this.checkIfPhoneNumberOrEmailIncluded()) {
      this.sendMessageForm.markAllAsTouched();
      return;
    }
    this.sendMessageForm.markAsUntouched();

    this.chatService
      .add(
        {
          ...this.sendMessageForm.getRawValue(),
          chatType: this.getValidFilterCount() < 2 ? ChatMessageType.INDIVIDUAL : this.sendMessageForm.controls.chatType.value
        },
        API_URL.octopusChatAppServices.sendMessage
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.toasterService.success(this.constants.successMessages.sentSuccessfully.replace('{item}', 'Message'));
          this.refreshData.emit();
          this.onCloseSideNav();
          this.cdr.detectChanges();
        },
        error: () => {
          this.cdr.detectChanges();
        }
      });
  }

  getUsersAccordingToRole(): void {
    this.roleUserService
      .add({}, API_URL.user.getUsers)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<UserAccordngToRole>) => {
          this.allUsers = res.result.items;
          this.filters.deskManager.options = this.allUsers
            .filter(user => user.roles[0].roleId === this.constants.roleIds.DESK_MANAGER)
            .map(user => ({ id: user.id, name: user.name + ' ' + user.surname }));

          this.filters.instructor.options = this.allUsers
            .filter(user => user.roles[0].roleId === this.constants.roleIds.INSTRUCTOR)
            .map(user => ({ id: user.id, name: user.name + ' ' + user.surname }));

          this.filters.supervisor.options = this.allUsers
            .filter(user => user.roles[0].roleId === this.constants.roleIds.SUPERVISOR)
            .map(user => ({ id: user.id, name: user.name + ' ' + user.surname }));

          this.filters.admin.options = this.allUsers
            .filter(user => user.roles[0].roleId === this.constants.roleIds.ADMIN)
            .map(user => ({ id: user.id, name: user.name + ' ' + user.surname }));
          this.cdr.detectChanges();
        }
      });
  }

  getStudentList(): void {
    this.dependentService
      .add({ page: 1, instructorFilter: [], instrumentFilter: [] }, API_URL.crud.getAll)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<Students>) => {
          this.filters.client.options = res.result.items.map(student => ({
            id: student.dependentInformation.id,
            name: student.dependentInformation.firstName + ' ' + student.dependentInformation.lastName,
            accountManagerId: student.dependentInformation.accountManagerId
          }));
          this.cdr.detectChanges();
        }
      });
  }

  setFormControlValue(controlName: string, value: number | string | ReceieverId[]): void {
    (this.sendMessageForm.controls as any)[controlName].setValue(value);
  }

  setRequiredBasedOnCondition(controlName: string, required: boolean): void {
    const control = this.sendMessageForm.get(controlName);
    if (required) {
      control?.setValidators([Validators.required]);
    } else {
      control?.clearValidators();
    }
    control?.updateValueAndValidity();
  }

  getTimeDiff(start: string, end: string): number {
    return CommonUtils.calculateTimeDifference(new Date(start), new Date(end));
  }

  removeFileFromAws(): void {
    this.uploadFileService
      .deleteFile(
        `${API_URL.uploadFile.deleteFileFromAws}?${API_URL.uploadFile.fileName}=${
          this.sendMessageForm.controls.fileDetails.getRawValue()?.filePath
        }`
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.sendMessageForm.controls.fileDetails.setValue(null);
          this.fileName = '';
          this.fileExtension = '';
          this.fullPath = '';
          this.fileUploaded = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.cdr.detectChanges();
        }
      });
  }

  onCloseSideNav(): void {
    this.closeSideNav.emit();
    this.sendMessageForm.reset();
  }
}
