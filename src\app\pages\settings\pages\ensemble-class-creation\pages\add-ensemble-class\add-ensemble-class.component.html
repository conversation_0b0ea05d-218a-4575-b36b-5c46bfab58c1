<div class="o-sidebar-wrapper">
  <div class="o-sidebar-header">
    <div class="title">Add Ensemble Class</div>
    <div class="action-btn-wrapper">
      <button
        mat-raised-button
        color="accent"
        class="mat-accent-btn back-btn"
        type="button"
        (click)="closeSideNavFun()">
        Close
      </button>
      <button
        mat-raised-button
        color="primary"
        class="mat-primary-btn"
        type="button"
        (click)="checkCapacity()"
        [appLoader]="showBtnLoader">
        Save
      </button>
    </div>
  </div>
  <div class="divider"></div>
  <div class="o-sidebar-body">
    <form [formGroup]="ensembleClassFormGroup">
      <div class="group-class-form-wrapper">
        <div class="field-wrapper">
          <label class="mb-0">Select Instrument</label>
          <div class="w-100">
            <app-multi-select-chips
              class="w-100 mat-select-custom"
              (selectedFilterValues)="getInstructors(); setInsrtumentIdDetails(); resetInstructors()"
              [filterDetail]="filterParams.instrument"></app-multi-select-chips>
            <mat-error
              *ngIf="
                !ensembleClassFormGroup.controls.assignedInstruments.value.length &&
                (ensembleClassFormGroup.controls.assignedInstruments.touched ||
                  ensembleClassFormGroup.controls.assignedInstruments.dirty)
              "
              class="mat-error-position">
              <app-error-messages [control]="ensembleClassFormGroup.controls.assignedInstruments"></app-error-messages>
            </mat-error>
          </div>
        </div>

        <div class="field-wrapper">
          <label class="required mb-0">Select Duration</label>
          <div>
            <div class="btn-typed-options-wrapper w-100">
              @for (duration of durations | enumToKeyValue; track $index) {
              <div
                *ngIf="duration.value !== durations.FORTY_FIVE"
                [ngClass]="{
                  'btn-typed-option': true,
                  active: ensembleClassFormGroup.controls.duration.value === duration.value
                }"
                (click)="setFormControlValue('duration', duration.value)">
                {{ duration.value }} Min
              </div>
              }
            </div>
            <mat-error
              *ngIf="
                !ensembleClassFormGroup.controls.duration.value &&
                (ensembleClassFormGroup.controls.duration.touched || ensembleClassFormGroup.controls.duration.dirty)
              ">
              <app-error-messages [control]="ensembleClassFormGroup.controls.duration"></app-error-messages>
            </mat-error>
          </div>
        </div>

        <div class="field-wrapper">
          <label class="required mb-0">Select Lesson Type</label>
          <div>
            <div class="single-btn-select-wrapper">
              @for (lessonType of constants.lessonTypeValueOptions; track $index) {
              <div
                [ngClass]="{ active: ensembleClassFormGroup.controls.lessonType.value === lessonType.value }"
                class="select-btn"
                (click)="setFormControlValue('lessonType', lessonType.value)">
                {{ lessonType.label }}
              </div>
              }
            </div>
            <mat-error
              *ngIf="
                !ensembleClassFormGroup.controls.lessonType.value &&
                (ensembleClassFormGroup.controls.lessonType.touched || ensembleClassFormGroup.controls.lessonType.dirty)
              "
              class="mat-error-position">
              <app-error-messages [control]="ensembleClassFormGroup.controls.lessonType"></app-error-messages>
            </mat-error>
          </div>
        </div>

        <div class="field-wrapper field-with-mat-inputs">
          <label>Description</label>
          <mat-form-field>
            <mat-label>Enter description here</mat-label>
            <textarea
              matInput
              formControlName="description"
              cdkTextareaAutosize
              cdkAutosizeMinRows="3"
              cdkAutosizeMaxRows="10"></textarea>
            <mat-error>
              <app-error-messages [control]="ensembleClassFormGroup.controls.description"></app-error-messages>
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field-wrapper field-with-mat-inputs">
          <label class="required">Select Location</label>
          <div class="w-100">
            <mat-form-field class="w-100 mat-select-custom">
              <mat-select
                formControlName="locationId"
                placeholder="Select Location"
                (selectionChange)="getRoomsAndInstructors()">
                <mat-option *ngFor="let location of locations" [value]="location.schoolLocations.id">
                  {{ location.schoolLocations.locationName }}
                </mat-option>
              </mat-select>
              <mat-error>
                <app-error-messages [control]="ensembleClassFormGroup.controls.locationId"></app-error-messages>
              </mat-error>
            </mat-form-field>
          </div>
        </div>

        @if (ensembleClassFormGroup.controls.locationId.value) {
        <div class="field-wrapper field-with-mat-inputs">
          <label class="required">Select Room</label>
          <div class="w-100">
            <mat-form-field class="w-100 mat-select-custom">
              <mat-select formControlName="roomId" placeholder="Select Room">
                @if (rooms && rooms.length) {
                <mat-option *ngFor="let room of rooms" [value]="room.roomDetail.id">
                  {{ room.roomDetail.roomName }}
                </mat-option>
                } @else {
                <mat-option>No Room Available</mat-option>
                }
              </mat-select>
              <mat-error>
                <app-error-messages [control]="ensembleClassFormGroup.controls.roomId"></app-error-messages>
              </mat-error>
            </mat-form-field>
          </div>
        </div>
        }

        <div class="field-content">
          <div class="field-wrapper field-with-mat-inputs">
            <label class="required">Client Capacity</label>
            <mat-form-field class="mat-select-custom">
              <input matInput type="number" placeholder="Client Capacity" formControlName="studentCapacity" />
              <mat-error>
                <app-error-messages [control]="ensembleClassFormGroup.controls.studentCapacity"></app-error-messages>
              </mat-error>
            </mat-form-field>
          </div>
          <!-- tobe used <div class="field-wrapper field-with-mat-inputs ms-4">
              <mat-checkbox class="mat-checkbox-custom mb-4" formControlName="isWaitlistAvailable"> Waitlist Available </mat-checkbox>
            </div> -->
        </div>

        <div class="field-wrapper field-with-mat-inputs">
          <label class="required">Start Date</label>
          <div class="field-content">
            <mat-form-field class="mat-start-date w-100">
              <input
                matInput
                [matDatepicker]="startPicker"
                (click)="startPicker.open()"
                formControlName="scheduleStartDate"
                (dateChange)="getSuggestedTimeAndInstructors(true); getDayOfWeek()"
                placeholder="Select Start Date"
                [min]="maxDate" />
              <mat-datepicker-toggle matSuffix [for]="startPicker"></mat-datepicker-toggle>
              <mat-datepicker #startPicker></mat-datepicker>
              <mat-error>
                <app-error-messages [control]="ensembleClassFormGroup.controls.scheduleStartDate"></app-error-messages>
              </mat-error>
            </mat-form-field>
          </div>
        </div>

        <div class="field-wrapper">
          <label class="required mb-0">Select Weekday(s)</label>
          <div>
            <div class="single-btn-select-wrapper">
              @for (day of constants.daysOfTheWeek; track $index) {
              <div
                class="select-btn"
                [ngClass]="{
                  active: ensembleClassFormGroup.controls.daysOfSchedule.value.toString() === day.value.toString()
                }"
                (click)="setFormControlValue('daysOfSchedule', day.value)">
                {{ day.label }}
              </div>
              }
            </div>
            <mat-error
              *ngIf="
                !ensembleClassFormGroup.controls.daysOfSchedule.value &&
                (ensembleClassFormGroup.controls.daysOfSchedule.touched ||
                  ensembleClassFormGroup.controls.daysOfSchedule.dirty)
              "
              class="mat-error-position">
              <app-error-messages [control]="ensembleClassFormGroup.controls.daysOfSchedule"></app-error-messages>
            </mat-error>
          </div>
        </div>

        @if (getInstructorAvailability.scheduleStartDate && getInstructorAvailability.scheduleEndDate &&
        getInstructorAvailability.locationId && getInstructorAvailability.daysOfSchedule &&
        getInstructorAvailability.duration) {
        <div class="field-wrapper">
          <label class="required mb-0">Select Instructor</label>
          <div class="w-100 mat-select-custom time">
            @if (showInstructorLoader) {
            <ng-container [ngTemplateOutlet]="showSpinner"></ng-container>
            } @else {
            <app-multi-select-chips
              [filterDetail]="filterParams.instructor"
              (selectedFilterValues)="getSuggestedTime(); setInstructorIdDetails()"></app-multi-select-chips>
            <mat-error
              *ngIf="
                !ensembleClassFormGroup.controls.assignedInstructors.value &&
                (ensembleClassFormGroup.controls.assignedInstructors.touched ||
                  ensembleClassFormGroup.controls.assignedInstructors.dirty)
              "
              class="mat-error-position">
              <app-error-messages [control]="ensembleClassFormGroup.controls.assignedInstructors"></app-error-messages>
            </mat-error>
            }
          </div>
        </div>
        }

        <div class="field-wrapper">
          <label class="required mb-0">Select Time Slot</label>
          <div class="w-100">
            <mat-form-field class="w-100 mat-select-custom time">
              <mat-select placeholder="Select Time" [(value)]="selectedTimeSlot">
                @if (suggestedTimeSlots?.length) {
                <mat-option
                  *ngFor="let suggestedTimeSlot of suggestedTimeSlots"
                  (click)="setStartAndEndTime(suggestedTimeSlot)"
                  [value]="suggestedTimeSlot.startTime + ' - ' + suggestedTimeSlot.endTime">
                  {{ suggestedTimeSlot.startTime | date : "shortTime" }} -
                  {{ suggestedTimeSlot.endTime | date : "shortTime" }}
                </mat-option>
                } @else if (!suggestedTimeSlots?.length && !filterParams.instructor.value) {
                <mat-option>Please select an Instructor to see time slots</mat-option>
                } @else {
                <mat-option>No Time Slot Available</mat-option>
                }
              </mat-select>
            </mat-form-field>
            <mat-error
              *ngIf="
                !ensembleClassFormGroup.controls.scheduleStartTime.value &&
                (ensembleClassFormGroup.controls.scheduleStartTime.touched ||
                  ensembleClassFormGroup.controls.scheduleStartTime.dirty)
              "
              class="mat-error-position">
              <app-error-messages [control]="ensembleClassFormGroup.controls.scheduleStartTime"></app-error-messages>
            </mat-error>
          </div>
        </div>

        <div class="field-wrapper field-with-mat-inputs">
          <label class="required">Select Revenue Category</label>
          <div class="w-100">
            <mat-form-field class="w-100 mat-select-custom">
              <mat-select formControlName="categoryId" placeholder="Select Revenue Category">
                <mat-option *ngFor="let revenueCategory of revenueCategories" [value]="revenueCategory.category.id">
                  {{ revenueCategory.category.categoryName }}
                </mat-option>
              </mat-select>
              <mat-error>
                <app-error-messages [control]="ensembleClassFormGroup.controls.categoryId"></app-error-messages>
              </mat-error>
            </mat-form-field>
          </div>
        </div>

        <div class="field-wrapper field-with-mat-inputs">
          <label class="required">Ensemble Class Name</label>
          <mat-form-field class="mat-select-custom">
            <input matInput placeholder="Ensemble Class Name" formControlName="ensembleClassName" />
            <mat-error>
              <app-error-messages [control]="ensembleClassFormGroup.controls.ensembleClassName"></app-error-messages>
            </mat-error>
          </mat-form-field>
        </div>
      </div>
    </form>
  </div>
</div>
<ng-template #showSpinner>
  <div class="instructor-loader-wrapper">
    <div class="loader"></div>
    <span class="instructor-loading">Please wait while the instructors are loading...</span>
  </div>
</ng-template>
