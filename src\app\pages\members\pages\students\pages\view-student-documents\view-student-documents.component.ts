import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { DependentInformations } from '../../models';
import { MatButtonModule } from '@angular/material/button';
import { SignedDocuments, SignedDocumentsInfo } from 'src/app/pages/user-document/models';
import { MatSidenavModule } from '@angular/material/sidenav';
import { PdfViewerComponent } from '../../../../../user-document/pages/pdf-viewer/pdf-viewer.component';
import { LocalDatePipe } from 'src/app/shared/pipe';
import { GoogleAnalyticsService } from 'src/app/shared/services';
import { PlanSummaryService } from 'src/app/pages/settings/pages/plan/services';

const DEPENDENCIES = {
  MODULES: [CommonModule, MatButtonModule, SharedModule, MatSidenavModule],
  COMPONENTS: [PdfViewerComponent],
  PIPES: [LocalDatePipe]
};

@Component({
  selector: 'app-view-student-documents',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.COMPONENTS, ...DEPENDENCIES.PIPES],
  templateUrl: './view-student-documents.component.html',
  styleUrl: './view-student-documents.component.scss'
})
export class ViewStudentDocumentsComponent extends BaseComponent implements OnChanges {
  @Input() selectedStudentDetails!: DependentInformations | undefined;
  @Input() studentDocuments!: Array<SignedDocuments>;

  documentInfo!: SignedDocumentsInfo;
  isPDFViewerSideNavOpen = false;

  @Output() closeSideNav = new EventEmitter<void>();

  constructor(private readonly googleAnalyticsService: GoogleAnalyticsService, private readonly planSummaryService: PlanSummaryService) {
    super();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['studentDocuments']?.currentValue) {
      this.studentDocuments = changes['studentDocuments']?.currentValue;
    }
  }

  openPDFViewer(documentInfo: SignedDocumentsInfo): void {
    this.isPDFViewerSideNavOpen = true;
    this.documentInfo = { ...documentInfo, isAgreementDone: true, accountManagerId: this.selectedStudentDetails?.accountManagerId ?? 0 };
    if (documentInfo.isAgreementDone && !documentInfo.isPaid) {
      this.setGoogleAnalyticsCheckoutEvent();
    }
  }

  setGoogleAnalyticsCheckoutEvent(): void {
    this.googleAnalyticsService.trackBeginCheckout({
      currency: 'USD',
      value: this.documentInfo?.firstPayment + this.documentInfo?.serviceFees + this.documentInfo?.registrationFees || 0,
      items: [this.googleAnalyticsService.createEcommerceItem(this.getEcommerceItem)]
    });
  }

  get getEcommerceItem() {
    return {
      name: this.getClassName(),
      instrumentName: this.documentInfo?.planInstrument,
      locationName: this.documentInfo?.locationName,
      instructorId: 0,
      scheduleStartTime: this.documentInfo?.planStartDate,
      scheduleEndTime: this.documentInfo?.planEndDate,
      discount: +this.documentInfo?.discountedAmount || 0,
      quantity: 1,
      price: this.documentInfo?.firstPayment + this.documentInfo?.serviceFees + this.documentInfo?.registrationFees || 0
    };
  }

  getClassName(): string {
    if (this.documentInfo?.isDDDPlan) {
      return `${this.documentInfo?.planInstrument} DDD Plan (${this.documentInfo?.planDetails?.[0]?.planDetail?.duration})`;
    } else if (this.documentInfo?.isEnsembleAvailable) {
      return `${this.documentInfo?.planInstrument} Ensemble Plan`;
    }
    return `Weekly Music Lessons - ${this.documentInfo?.planInstrument} (${this.planSummaryService.getPlanType(
      this.documentInfo?.planType!
    )} ${this.planSummaryService.getPlanSummary(this.documentInfo?.planDetails!)})`;
  }

  closeViewAllDocument(): void {
    this.closeSideNav.emit();
  }
}
