import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { FormArray, FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { DependentInfo, DependentInfoForm, ProfileBeforeSignUpParams, SignUpForOptions, SignUpTypeForm } from 'src/app/auth/models';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { provideNativeDateAdapter } from '@angular/material/core';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { NgxMaskDirective, provideNgxMask } from 'ngx-mask';
import { AuthService } from 'src/app/auth/services';
import { takeUntil } from 'rxjs';
import { CBGetResponse, CBResponse, MatDialogRes } from 'src/app/shared/models';
import { ActivatedRoute } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
import { ConfirmationDialogComponent } from 'src/app/shared/components/confirmation-dialog/confirmation-dialog.component';
import { SchoolLocations } from 'src/app/pages/room-and-location-management/models';
import { dateOfBirthValidator, phoneNumberValidator, zipCodeValidator } from 'src/app/shared/validators';
import { CommonService } from 'src/app/shared/services';
import { State } from 'src/app/auth/models/user.model';
import { MatTooltipModule } from '@angular/material/tooltip';

const DEPENDENCIES = {
  MODULES: [
    SharedModule,
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    MatSelectModule,
    MatDatepickerModule,
    MatCheckboxModule,
    NgxMaskDirective,
    MatTooltipModule
  ],
  COMPONENTS: []
};

@Component({
  selector: 'app-sign-up-type',
  standalone: true,
  imports: [...DEPENDENCIES.COMPONENTS, ...DEPENDENCIES.MODULES],
  providers: [provideNativeDateAdapter(), provideNgxMask()],
  templateUrl: './sign-up-type.component.html',
  styleUrl: './sign-up-type.component.scss'
})
export class SignUpTypeComponent extends BaseComponent implements OnInit {
  @Input() profileBeforeSignUpParams!: ProfileBeforeSignUpParams;
  @Input() referralToEmail!: string | undefined;
  @Input() isAddDependent!: boolean;

  signUpTypeFormGroup!: FormGroup<SignUpTypeForm>;
  signUpForOptions = SignUpForOptions;
  locations!: Array<SchoolLocations>;
  states!: Array<State>;
  deleteChildIds: number[] = [];
  maxDate = new Date();
  previousDependentDetails: DependentInfo[] = [];

  constructor(
    private readonly authService: AuthService,
    private readonly activatedRoute: ActivatedRoute,
    private readonly commonService: CommonService,
    private readonly dialog: MatDialog,
    private readonly cdr: ChangeDetectorRef
  ) {
    super();
  }

  ngOnInit(): void {
    this.initSignUpTypeForm();
    this.getAllLocations();
    this.getAllStates();
  }

  initSignUpTypeForm(): void {
    this.signUpTypeFormGroup = new FormGroup<SignUpTypeForm>({
      firstName: new FormControl(this.profileBeforeSignUpParams?.firstName ?? '', {
        nonNullable: true,
        validators: [Validators.required]
      }),
      lastName: new FormControl(this.profileBeforeSignUpParams?.lastName ?? '', {
        nonNullable: true,
        validators: [Validators.required]
      }),
      locationId: new FormControl(this.profileBeforeSignUpParams?.locationId ?? undefined, {
        nonNullable: true,
        validators: [Validators.required]
      }),
      stateId: new FormControl(this.profileBeforeSignUpParams?.stateId ?? undefined, {
        nonNullable: true,
        validators: [Validators.required]
      }),
      city: new FormControl(this.profileBeforeSignUpParams?.city ?? '', {
        nonNullable: true,
        validators: [Validators.required]
      }),
      zipCode: new FormControl(this.profileBeforeSignUpParams?.zipCode ?? undefined, {
        nonNullable: true,
        validators: [Validators.required, zipCodeValidator()]
      }),
      dateOfBirth: new FormControl(this.profileBeforeSignUpParams?.dateOfBirth ?? '', {
        nonNullable: true,
        validators: [Validators.required, dateOfBirthValidator()]
      }),
      emailAddress: new FormControl(this.referralToEmail ?? this.profileBeforeSignUpParams?.emailAddress ?? '', {
        nonNullable: true,
        validators: [Validators.required, Validators.pattern(this.constants.pattern.EMAIL)]
      }),
      phoneNumber: new FormControl(this.profileBeforeSignUpParams?.phoneNumber ?? '', {
        nonNullable: true,
        validators: [Validators.required, phoneNumberValidator()]
      }),
      address: new FormControl(this.profileBeforeSignUpParams?.address ?? '', { nonNullable: true, validators: [Validators.required] }),
      dependentId: new FormControl(this.profileBeforeSignUpParams?.dependentId ?? undefined, {
        nonNullable: true,
        validators: [Validators.required]
      }),
      userId: new FormControl(this.profileBeforeSignUpParams?.userId ?? 0, {
        nonNullable: true
      }),
      dependentDetails: new FormArray([] as FormGroup<DependentInfoForm>[]),
      allAttendedAtSameLocation: new FormControl(this.profileBeforeSignUpParams?.allAttendedAtSameLocation ?? true, {
        nonNullable: true
      }),
      isNoSubstitute: new FormControl(this.profileBeforeSignUpParams?.isNoSubstitute ?? false, {
        nonNullable: true
      })
    });
    this.setDependentInfo();
    this.setEmailAddress();
    this.cdr.detectChanges();
  }

  setEmailAddress(): void {
    this.activatedRoute.queryParams.subscribe((params: any) => {
      if (params?.email) {
        this.signUpTypeFormGroup.controls.emailAddress.setValue(params.email);
        this.signUpTypeFormGroup.controls.emailAddress.disable();
      }
    });
    if (this.profileBeforeSignUpParams?.emailAddress) {
      this.signUpTypeFormGroup.controls.emailAddress.disable();
    }
  }

  setDependentInfo(): void {
    if (this.profileBeforeSignUpParams?.dependentDetails?.length) {
      const dependentInfoFormArray = this.signUpTypeFormGroup.get('dependentDetails') as FormArray;
      dependentInfoFormArray.clear();
      this.profileBeforeSignUpParams.dependentDetails.forEach(info => {
        dependentInfoFormArray.push(
          new FormGroup({
            firstName: new FormControl(info.firstName ?? '', {
              nonNullable: true,
              validators: [Validators.required]
            }),
            lastName: new FormControl(info.lastName ?? '', {
              nonNullable: true,
              validators: [Validators.required]
            }),
            dateOfBirth: new FormControl(info.dateOfBirth ?? '', {
              nonNullable: true,
              validators: [Validators.required]
            }),
            locationId: new FormControl(info.locationId ?? undefined, {
              nonNullable: true,
              validators: [Validators.required]
            }),
            isNoSubstitute: new FormControl(info.isNoSubstitute ?? false, {
              nonNullable: true
            }),
            id: new FormControl(info.id ?? undefined, { nonNullable: true })
          })
        );
      });
    }
  }

  setDependentId(dependentId: number): void {
    const formControls = this.signUpTypeFormGroup.controls;

    if (dependentId === this.signUpForOptions.YOURSELF) {
      if (formControls.dependentDetails.value.length) {
        this.previousDependentDetails = [...(formControls.dependentDetails.value as DependentInfo[])];
      }
      formControls.dependentDetails.clear();
    } else {
      if (!formControls.dependentDetails.value.length) {
        if (this.previousDependentDetails.length) {
          this.previousDependentDetails.forEach(detail => this.addMoreChild(detail));
        } else {
          this.addMoreChild();
        }
      }
    }

    formControls.dependentId.setValue(dependentId);
  }

  deleteDependent(dependentIndex: number, dependentId?: number): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: `Delete Dependent Information`,
        message: `Are you sure you want to delete this dependent information?`
      }
    });

    dialogRef.afterClosed().subscribe((result: MatDialogRes) => {
      if (result.isConfirmed) {
        this.getDependentInfoFormArray.removeAt(dependentIndex);
        if (dependentId) this.deleteChildIds.push(dependentId);
        this.cdr.detectChanges();
      }
    });
  }

  getAllLocations(): void {
    this.commonService
      .getLocations()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<SchoolLocations>) => {
          this.locations = res.result.items;
          this.cdr.detectChanges();
        }
      });
  }

  getAllStates(): void {
    this.commonService
      .getStates()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<Array<State>>) => {
          this.states = res.result;
          this.cdr.detectChanges();
        }
      });
  }

  get getDependentInfoFormArray(): FormArray {
    return this.signUpTypeFormGroup?.get('dependentDetails') as FormArray;
  }

  addMoreChild(details?: DependentInfo): void {
    this.getDependentInfoFormArray.push(
      new FormGroup({
        firstName: new FormControl(details?.firstName ?? '', {
          nonNullable: true,
          validators: [Validators.required]
        }),
        lastName: new FormControl(details?.lastName ?? '', {
          nonNullable: true,
          validators: [Validators.required]
        }),
        dateOfBirth: new FormControl(details?.dateOfBirth ?? '', { nonNullable: true, validators: [Validators.required] }),
        locationId: new FormControl(details?.locationId ?? undefined, { nonNullable: true, validators: [Validators.required] }),
        isNoSubstitute: new FormControl(details?.isNoSubstitute ?? false, { nonNullable: true }),
        id: new FormControl(details?.id ?? undefined, { nonNullable: true })
      })
    );
  }
}
