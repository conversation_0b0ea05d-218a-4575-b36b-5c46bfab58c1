<mat-sidenav-container class="example-container" [hasBackdrop]="true">
  <mat-sidenav
    [opened]="isSideNavOpen || isMessageSideNavOpen"
    mode="over"
    position="end"
    fixedInViewport="true"
    class="lg-sidebar"
    [disableClose]="true">
    @if (isSideNavOpen) {
      <app-instructor-under-supervisor
        [selectedSupervisorDetail]="selectedInstructorViewDetails"
        (closeSideNav)="isSideNavOpen = false"></app-instructor-under-supervisor>
    }
    @if (isMessageSideNavOpen) {
      <app-messages
        (closeSideNav)="toggleMessageSideNav(false)"
        [selectedIdEmail]="selectedIdEmail"
      ></app-messages>
    }
  </mat-sidenav>
</mat-sidenav-container>

<ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : viewInstructorTemplate"></ng-container>

<ng-template #viewInstructorTemplate>
  <div class="o-sidebar-wrapper">
    <div class="o-sidebar-header">
      <div class="d-flex align-items-center">
        <div class="instructor-header">
          @if (selectedInstructorViewDetails?.profilePhoto) {
            <img [src]="selectedInstructorViewDetails?.profilePhoto" alt="" />
          } @else {
            <div class="placeholder-name">
              <div>
                {{ getInitials(selectedInstructorViewDetails?.name) | uppercase }}
              </div>
            </div>
          }
        </div>
        <div>
          <div class="name">
            {{ selectedInstructorViewDetails?.name }}
          </div>
          <div class="instructor-details-wrapper">
            <div class="instructor-details">
              <img [src]="constants.staticImages.icons.email" alt="" class="me-1" />
              {{ selectedInstructorViewDetails?.email }}
            </div>
            <div class="dot"></div>
            <div class="instructor-details">
              <img [src]="constants.staticImages.icons.phone" alt="" class="me-1" />
              {{ selectedInstructorViewDetails?.phoneNumber }}
            </div>
            @if (isSupervisor) {
              <div class="dot"></div>
              <div class="instructor-details pointer" (click)="isSideNavOpen = true">
                <img [src]="constants.staticImages.icons.memberIcon" alt="" class="me-1" />
                {{ selectedInstructorViewDetails?.instructors ?? 0 }} <span class="text-gray ms-1">Instructors</span>
              </div>
            }
            <div class="dot"></div>
            <div class="instructor-details">
              <img [src]="constants.staticImages.icons.student" alt="" class="me-1" />
              {{ selectedInstructorViewDetails?.students ?? 0 }} <span class="text-gray ms-1">Clients</span>
            </div>
          </div>
        </div>
      </div>
      <div class="action-btn-wrapper">
        <button
          mat-raised-button
          color="accent"
          class="mat-accent-btn back-btn"
          type="button"
          (click)="closeViewSideNavFun()">
          Close
        </button>
        @if (!isFromScheduler) {
          <button
            *appHasPermission="[constants.roles.ADMIN, constants.roles.DESK_MANAGER]"
            mat-raised-button
            color="primary"
            class="mat-primary-btn"
            type="button"
            (click)="onEdit()"
            [appLoader]="showBtnLoader">
            Edit
          </button>
          <button
            *appHasPermission="[constants.roles.ADMIN, constants.roles.DESK_MANAGER]"
            mat-raised-button
            color="accent"
            class="mat-red-btn back-btn ms-1"
            type="button"
            (click)="onDeleteConfirmation()"
            [appLoader]="showBtnLoader">
            Delete
          </button>
        }
      </div>
    </div>
    <div class="divider"></div>
    <div class="o-sidebar-body">
      <div class="row">
        <div class="col-md-7">
          <div class="o-location-info mb-3">
          <app-members-notes
            [instructorId]="selectedInstructorViewDetails!.id"
          ></app-members-notes>
          </div>
          <div class="o-location-info mb-3" *appHasPermission="[constants.roles.ADMIN, constants.roles.DESK_MANAGER]">
            <div class="header-wrapper">
              <div class="d-flex align-items-center">
                <img [src]="constants.staticImages.icons.message" alt="" class="black-img" />
                <div class="main-title">Messages</div>
              </div>
              <div class="pointer primary-color" (click)="toggleMessageSideNav(true)">View All</div>
            </div>
            <ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : messageTemp"></ng-container>
          </div>
          <div class="o-location-info mb-3">
            <div class="header-wrapper">
              <div class="d-flex align-items-center">
                <img [src]="constants.staticImages.icons.calendarIcon" alt="" class="black-img" />
                <div class="main-title">Schedule</div>
              </div>
            </div>
            <div class="instructor-header-wrapper">
              <div (click)="updateDay(-1)"><mat-icon>keyboard_arrow_left</mat-icon></div>
              <div class="date">
                <mat-form-field class="search-bar-wrapper me-2">
                  <input
                    matInput
                    [matDatepicker]="startDatePicker"
                    [(ngModel)]="filters.startDate"
                    (click)="startDatePicker.open()"
                    (dateChange)="getDependentSchedule(filters.endDate, filters.endDate)"
                    placeholder="Select Date"
                    readonly />
                  <mat-datepicker-toggle matSuffix [for]="startDatePicker"></mat-datepicker-toggle>
                  <mat-datepicker #startDatePicker></mat-datepicker>
                </mat-form-field>
              </div>
              <div (click)="updateDay(1)"><mat-icon>keyboard_arrow_right</mat-icon></div>
            </div>
            <ng-container [ngTemplateOutlet]="showScheduleLoader ? showLoader : scheduleTimeline"></ng-container>
          </div>
          <div class="o-location-info">
            <div class="header-wrapper">
              <div class="d-flex align-items-center">
                <img [src]="constants.staticImages.icons.timeCircleClock" alt="" class="black-img" />
                <div class="main-title">Monthly Working Hours</div>
              </div>
              <div class="pointer primary-color">
                <app-month-year-picker [selectedYear]="selectedYear" (refreshData)="getInstructorWorkingHours($event)"></app-month-year-picker>
              </div>
            </div>
            <div class="leave-dashboard">
              <app-chart-viewer [chartOption]="workingHoursChart" [isWorkingHoursChart]="true"></app-chart-viewer>
              <div class="ms-4">
                    <div>
                      <div class="total-leaves">
                        {{ formatHoursToHM(workingHours.totalWorkingHours) }}
                        <div class="content">Total Working Hours</div>
                      </div>
                      <div class="used-leaves mb-2">
                        <div class="dot used-dot"></div>Pending Hours: {{ formatHoursToHM(workingHours.pendingWorkingHours ?? 0) || 0 }}
                      </div>
                      <div class="available-leaves">
                        <div class="dot available-dot"></div>Completed Hours: {{ formatHoursToHM(workingHours.totalActualHours) || 0 }}
                      </div>
                    </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-5">
          <div class="o-location-info min-height-0">
            <div class="header-wrapper">
              <div class="d-flex align-items-center">
                <img [src]="constants.staticImages.icons.files" alt="" class="black-img" />
                <div class="main-title">Location & Availability</div>
              </div>
            </div>
            <div class="room-details">
              <div class="d-flex">
                <img [src]="constants.staticImages.icons.musicWave" alt="" />
                <div class="sub-title">Available for Introductory Lessons</div>
              </div>
              <div class="content flex-wrap">
                @for (instrument of selectedInstructorViewDetails?.instruments; track $index) {
                  @if (instrument.isIntroductoryClassAvailable) {
                    <div class="dot"></div>
                    {{ instrument.name }} <span class="primary-color ms-1">{{ instrument.gradeLevel }}</span>
                  }
                }
              </div>
              <div class="content" *ngIf="noIntroductoryInstrumentAvailable">
                No Introductory Instruments Available.
              </div>
              <div class="dotted-divider" *ngIf="selectedInstructorViewDetails?.instructorAvailability?.length"></div>
            </div>
            <div *ngFor="let location of selectedInstructorViewDetails?.instructorAvailability; let $index = index">
              <div *ngIf="!isLocationCollapsed || $index === 0">
                <div class="room-details">
                  <div class="d-flex">
                    <img [src]="constants.staticImages.icons.location" alt="" />
                    <div class="sub-title">Location and Room</div>
                  </div>
                  <div class="content">{{ location.locationName }} - {{ location.roomName }}</div>
                </div>
                <div class="room-details">
                  <div class="d-flex">
                    <img [src]="constants.staticImages.icons.calendarIcon" alt="" />
                    <div class="sub-title">Starting Date</div>
                  </div>
                  <div class="content">{{ location.availableStartDate | date: constants.dateFormat }}</div>
                </div>
                <div class="room-details">
                  <div class="d-flex">
                    <img [src]="constants.staticImages.icons.timeCircleClock" alt="" />
                    <div class="sub-title">Start Time - End Time</div>
                  </div>
                  <div class="content">
                    {{ location.availableStartTime | date: "shortTime" }} -
                    {{ location.availableEndTime | date: "shortTime" }}
                  </div>
                </div>
                <div class="room-details">
                  <div class="d-flex">
                    <img [src]="constants.staticImages.icons.repeatType" alt="" />
                    <div class="sub-title">Availability Repeats Types</div>
                  </div>
                  <div class="content">
                    {{ getAvailabilityTypeName(location.availabilityType).replace("_", " ") | titlecase }}
                    <div class="dot d-inline-block"></div>
                    <div>
                      End Date
                      <span class="primary-color">{{ location.availableEndDate | date: constants.dateFormat }}</span>
                    </div>
                  </div>
                </div>
                <div class="room-details">
                  <div class="d-flex">
                    <img [src]="constants.staticImages.icons.repeatType" alt="" />
                    <div class="sub-title">Available Days</div>
                  </div>
                  <div class="content">
                    {{ getAvailableDays(location.availableDays) }}
                  </div>
                </div>
              </div>
              <div
                class="dotted-divider"
                *ngIf="
                  (isLocationCollapsed && $index === 1) ||
                  (!isLocationCollapsed && $index < selectedInstructorViewDetails?.instructorAvailability?.length! - 1)
                "></div>
            </div>
            <div
              *ngIf="selectedInstructorViewDetails?.instructorAvailability?.length! > 1 && !isLocationCollapsed"
              class="dotted-divider"></div>
            <div *ngIf="selectedInstructorViewDetails?.instructorAvailability?.length! > 1">
              <div (click)="toggleLocationCollapse()" class="show-more-less">
                {{ isLocationCollapsed ? "Show More" : "Show Less" }}
                <span *ngIf="isLocationCollapsed"
                  >({{ selectedInstructorViewDetails?.instructorAvailability?.length! - 1 }})</span
                >
              </div>
            </div>
          </div>
          <div class="basic-details mt-3">
            <div class="header-wrapper">
              <div class="d-flex align-items-center mb-2">
                <img [src]="constants.staticImages.icons.files" alt="" class="black-img" />
                <div class="main-title">Basic Details</div>
              </div>
            </div>
            <div>
              <div class="room-details">
                <div class="d-flex">
                  <img [src]="constants.staticImages.icons.calendarIcon" alt="" />
                  <div class="sub-title">DOB</div>
                </div>
                <div class="content">
                  {{ selectedInstructorViewDetails?.dateOfBirth | date: constants.dateFormat | dashIfEmpty }}
                </div>
              </div>
              <div class="room-details">
                <div class="d-flex">
                  <img [src]="constants.staticImages.icons.location" alt="" />
                  <div class="sub-title">Address</div>
                </div>
                <div class="content">{{ selectedInstructorViewDetails?.address | dashIfEmpty }}, {{  selectedInstructorViewDetails?.stateAbbreviation | dashIfEmpty }}, {{ selectedInstructorViewDetails?.city | titlecase | dashIfEmpty }}, {{  selectedInstructorViewDetails?.zipCode | dashIfEmpty }}</div>
              </div>
              <div class="room-details">
                <div class="d-flex">
                  <img [src]="constants.staticImages.icons.musicWave" alt="" />
                  <div class="sub-title">Instrument skills</div>
                </div>
                <div class="content flex-wrap">
                  @for (instrument of selectedInstructorViewDetails?.instruments; track $index) {
                    <div class="dot"></div>
                    <div class="instrument">
                      {{ instrument.name }} <span class="primary-color">{{ instrument.gradeLevel }}</span>
                    </div>
                  }
                </div>
              </div>
              <div class="room-details">
                <div class="d-flex">
                  <img [src]="constants.staticImages.icons.profileIcon" alt="" />
                  <div class="sub-title">Bio</div>
                </div>
                <div class="content">
                  @if (selectedInstructorViewDetails?.bio | dashIfEmpty) {
                    <ng-container [ngTemplateOutlet]="showBio"></ng-container>
                  }
                </div>
              </div>
            </div>
          </div>
          <div class="basic-details mt-3" *appHasPermission="[constants.roles.ADMIN, constants.roles.DESK_MANAGER]">
            <div class="header-wrapper">
              <div class="d-flex align-items-center mb-2">
                <img [src]="constants.staticImages.icons.chartLineStar" alt="" class="black-img" />
                <div class="main-title">Leave Balance</div>
              </div>
              <div class="pointer" (click)="onEdit()">
                <img [src]="constants.staticImages.icons.editPenGreen" alt="" class="green-img">
              </div>
            </div>
            <div>
              <div class="leave-dashboard">
                <ng-container [ngTemplateOutlet]="leaveBalanceTemp"></ng-container>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #showBio>
  <div>
    {{ selectedInstructorViewDetails?.bio | slice: 0 : (isBioCollapsed ? 150 : undefined) | dashIfEmpty }}
    <span
      *ngIf="selectedInstructorViewDetails?.bio"
      class="pointer primary-color"
      (click)="isBioCollapsed = !isBioCollapsed">
      {{ isBioCollapsed ? "...Show More" : "Show Less" }}
    </span>
  </div>
</ng-template>

<ng-template #scheduleTimeline>
  <ng-container
    [ngTemplateOutlet]="events && events.length ? timelineContainer : noDataFound"
    [ngTemplateOutletContext]="{ data: 'Schedule' }"></ng-container>
</ng-template>

<ng-template #timelineContainer>
  <div class="timeline-container">
    @for (event of events; track $index; let last = $last) {
      <div class="timeline-item">
        <div class="time">{{ event.start | localDate | date: constants.dateFormats.hh_mm_a }}</div>
        <div
          class="dot-with-line"
          [ngStyle]="{ border: '4px solid ' + (event.instrumentFontColor || constants.colors.blueColor) }"
          [ngClass]="{ 'no-line': last }"></div>
        <div class="details">
          <div class="instrument-detail" [ngStyle]="{ color: event.instrumentFontColor || constants.colors.blueColor }">
            <span [ngClass]="{ strike: event.isCancelSchedule }">
              <span *ngIf="event.isCancelSchedule">
                @if (event.isLateCancelSchedule) {
                  Late Canceled: 
                } @else {
                  Canceled: 
                }
              </span>
              <span *ngIf="event.isDraftSchedule">Draft: </span>
              @if (event.classType === classTypes.GROUP_CLASS) {
                <span *ngIf="event.classType === classTypes.GROUP_CLASS" class="name">{{ event.groupClassName }}</span>
              }@else if (event.classType === classTypes.ENSEMBLE_CLASS) {
                <span class="name">{{ event.ensembleClassName }}</span>
              }
              
              @else {
                <span class="name"
                  >{{ event.instrumentName ? event.instrumentName + " Lessons" : event.campName }}
                </span>
              }
              <span class="duration">({{ getTimeDiff(event.start, event.end) }})</span>
            </span>
          </div>
          <div class="additional-info">
            @if (event.studentDetails.length) {
              <div>
                <img [src]="constants.staticImages.icons.memberIcon" alt="" />
                <span class="pointer" (click)="openStudentDetails(event.studentDetails[0].studentId)"
                  >{{ event.studentDetails[0].studentName }}
                  @if (event.studentDetails.length > 1) {
                    <span class="remaining-instructor-available-count"> +{{ event.studentDetails.length - 1 }} </span>
                  }
                </span>
              </div>
              @if (event.classType !== (classTypes.SUMMER_CAMP || classTypes.GROUP_CLASS || classTypes.ENSEMBLE_CLASS)) {
                <div class="dot"></div>
                <div>
                  <img [src]="constants.staticImages.icons.profileCircle" alt="" />
                  <span>{{ event.studentDetails[0].studentAge | number: "1.0-0" }} years</span>
                </div>
              }
            }
            @if (event.roomName) {
              <div class="dot"></div>
              <div class="d-flex align-items-center">
                <img [src]="constants.staticImages.icons.home" alt="" />
                <span>{{ event.roomName }}</span>
              </div>
            }
          </div>
        </div>
      </div>
    }
  </div>
</ng-template>

<ng-template #messageTemp>
  <ng-container [ngTemplateOutlet]="chatHistory && chatHistory.length ? chatHistoryTemp : noDataFound" [ngTemplateOutletContext]="{ data: 'Message' }"></ng-container>
</ng-template>

<ng-template #chatHistoryTemp>
  @for (chat of chatHistory; track $index) {
    <div class="chat-message" [ngClass]="{'mb-3': !$last}">
      <div class="chat-image">
        @if (chat.chatImage) {
          <img [src]="chat.chatImage" class="img me-3" alt="" />
          } @else {
          <div class="placeholder-name">
            <div>
              {{ getInitials(chat.chatName) | uppercase }}
            </div>
          </div>
          }
      </div>
      <div class="chat-details">
        <div class="chat-header">
          <div class="chat-name" [matTooltip]="chat.chatName">{{ chat.chatName | titlecase }}</div>
          <div class="chat-type" [ngClass]="{'public': chat.chatType === chatTypes.PUBLIC, 'private': chat.chatType === chatTypes.PRIVATE}">
            @if(chat.chatType === chatTypes.PUBLIC) {
              <img [src]="constants.staticImages.icons.openLock" class="lock" alt=""> Public
            }
            @else if (chat.chatType === chatTypes.PRIVATE) {
              <img [src]="constants.staticImages.icons.lock" class="lock" alt=""> Private
            }
          </div>
        </div>
        <div class="chat-message-text">
          @if(chat.isLastMessageDeleted) {
            <div class="message">This message was deleted.</div>
          }
          @else {
            @if(chat.lastMessage) {
            <div class="message text-truncate">{{ chat.lastMessage }}</div>
            }
            @else {
              @switch (chat.lastMessageFile.fileType) {
                @case (fileTypes.IMAGE) {
                <div class="message">Sent an image</div>
                }
                @case (fileTypes.VIDEO) {
                <div class="message">Sent a video</div>
                }
                @default {
                <div class="message">Sent a file</div>
                }
              }
            }
          }
        </div>
        <div class="text-gray">{{ chat.localLatestMessageTime | date: constants.fullDate }} at {{ chat.localLatestMessageTime | date: 'shortTime' }}</div>
      </div>
    </div>
  }
</ng-template>

<ng-template #leaveBalanceTemp>
  <app-chart-viewer [chartOption]="chartOptions"></app-chart-viewer>
  <div class="ms-4">
      <div>
          <div class="total-leaves">
              {{ totalLeaves }} <div class="content">Total Leaves</div>
          </div>
          <div class="used-leaves mb-2">
              <div class="dot used-dot"></div> Used Leaves: {{ usedLeaves }}
          </div>
          <div class="available-leaves">
              <div class="dot available-dot"></div> Available Leaves: {{ availableLeaves }}
          </div>
      </div>
  </div>
</ng-template>

<ng-template #showLoader>
  <div class="page-loader-wrapper">
    <app-content-loader></app-content-loader>
  </div>
</ng-template>

<ng-template #noDataFound let-data="data">
  <div class="no-data-found-wrapper">
    <h3>No {{ data | titlecase }} Available!</h3>
  </div>
</ng-template>
