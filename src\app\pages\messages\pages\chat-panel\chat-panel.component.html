<div class="o-sidebar-wrapper">
  <div class="o-sidebar-header">
    <div>
      <div class="title" [matTooltip]="selectedChat!.chatName">{{ getChatName(selectedChat!.chatName) }}</div>
      <div>{{ getRoleName(selectedChat?.chatType) }}</div>
    </div>
    <div class="action-btn-wrapper">
      <button mat-raised-button color="accent" class="mat-accent-btn back-btn" type="button" (click)="onCloseSideNav()">Close</button>
    </div>
  </div>
  <div class="divider"></div>
  <div class="o-sidebar-body">
    <div class="send-message-container">
      <div
        class="chat-window" 
        [ngClass]="{'admin-window': selectedChat?.userEmail}"
        infiniteScroll
        [infiniteScrollDistance]="constants.infiniteScrollConfig.infiniteScrollDistance"
        [infiniteScrollUpDistance]="constants.infiniteScrollConfig.infiniteScrollUpDistance"
        [infiniteScrollThrottle]="constants.infiniteScrollConfig.infiniteScrollThrottle"
        [scrollWindow]="false"
        (scrolledUp)="onScroll()"
      >
        @for (pastMessage of pastMessages; track $index) {
          <div 
              [ngClass]="pastMessage.isSendByMe ? 'sent-message' : 'received-message'" 
              #chatMessage 
              (mouseenter)="setHoveredMessageId(pastMessage.messageId)" 
              (mouseleave)="setHoveredMessageId(null)"
            >
  
              @if (pastMessage.isDeleted && pastMessage.messageId) {
                @if (pastMessage.isSendByMe) {
                  <div class="deleted-msg"> 
                    <mat-icon>block</mat-icon> You deleted this message.
                  </div>
                }
                @else {
                  <div class="deleted-msg"> 
                    <mat-icon>block</mat-icon> This message was deleted.
                  </div>
                }
              }
              @else {
                @if(pastMessage.fileDetails) { 
                  @switch (pastMessage.fileDetails.fileType) { 
                    @case (fileTypes.DOCUMENT) {
                      <a [href]="pastMessage.fileDetails.filePath" target="_blank" #openFile rel="noopener noreferrer"></a>
                      <div 
                        class="file-uploaded" 
                        [ngClass]="{ 'mb-2': pastMessage.message }" 
                        (click)="openFile.click()"
                      >
                        @if(pastMessage.fileDetails.fileExtension.includes('pdf')) {
                          <img [src]="constants.staticImages.images.pdfImage" alt="" />
                        } @else if(pastMessage.fileDetails.fileExtension.includes('doc')) {
                          <img [src]="constants.staticImages.images.docImage" alt="" />
                        } @else if(pastMessage.fileDetails.fileExtension.includes('xls')) {
                          <img [src]="constants.staticImages.images.xlsImage" alt="" />
                        } @else if(pastMessage.fileDetails.fileExtension.includes('txt')) {
                          <img [src]="constants.staticImages.images.txtImage" alt="" />
                        }
                        {{ pastMessage.fileDetails.fileName }}
                      </div>
                    } 
                    @case (fileTypes.VIDEO) {
                      <video width="260" height="180" controls>
                        <source [src]="pastMessage.fileDetails.filePath" />
                      </video>
                    } 
                    @default {
                      <div class="image-container">
                        <img 
                          [src]="pastMessage.fileDetails.filePath" 
                          (click)="openImagePreview(pastMessage.fileDetails)" 
                          alt="img" 
                          width="150" 
                          height="150" 
                          class="uploaded-img" 
                        />
                      </div>
                    } 
                  } 
                }
                <div>{{ pastMessage.message }}</div>
              }
      
              <div 
                class="menu-icon" 
                *ngIf="hoveredMessageId === pastMessage.messageId && ((pastMessage.isSendByMe && !pastMessage.isDeleted) || pastMessage.fileDetails)"  
                (click)="toggleMessageMenu()"
              >
                <mat-icon>more_vert</mat-icon>
              </div>
      
              <div 
                class="message-option-menu" 
                *ngIf="isMessageMenuOpen && hoveredMessageId === pastMessage.messageId" 
                [ngClass]="pastMessage.isSendByMe ? 'end-0' : 'start-0'"
              >
                <div *ngIf="pastMessage.fileDetails"
                  (click)="onDownloadImage(pastMessage.fileDetails.fileRelativePath!)" 
                  class="message-option"
                >
                  <mat-icon>download</mat-icon> Download
                </div>
                <div *ngIf="pastMessage.isSendByMe && !selectedChat?.userEmail"
                  (click)="onDeleteMessage(pastMessage, $first)" 
                  class="message-option"
                >
                  <mat-icon>delete</mat-icon> Delete
                </div>
              </div>
          </div>
      
          <div 
              class="d-flex align-items-center mt-3" 
              [ngClass]="{ 'align-self-end': pastMessage.isSendByMe }"
            >
              <div *ngIf="!pastMessage.isSendByMe" class="sender">
                {{ pastMessage.senderName }}
              </div>
              <div class="send-time">
                {{ pastMessage.localSendTime | date : constants.dateFormats.hh_mm_a }}
              </div>
          </div>

          <div class="date-header" *ngIf="pastMessage.header">{{ pastMessage.header }}</div>
        }
    
        <div class="loading-container" *ngIf="isLoading && pastMessages.length">
          <ng-container [ngTemplateOutlet]="showLoader"></ng-container>
        </div>
      </div>
      
      @if (!selectedChat?.userEmail) {
        <form [formGroup]="sendMessageForm">
          <div 
            class="chat-input"
            [ngClass]="{
              'expanded-input' : fileName && getFileType() !== fileTypes.DOCUMENT, 
              'expanded-input-file' : fileName && getFileType() === fileTypes.DOCUMENT 
            }"
          >
          @if(fileName) {
            @switch (getFileType()) {
              @case (fileTypes.DOCUMENT) {
                <ng-container 
                  [ngTemplateOutlet]="fileUploaded ? docThumbnail : showUploadSpinner"
                ></ng-container>
              }
              @case (fileTypes.IMAGE) {
                <ng-container 
                  [ngTemplateOutlet]="fileUploaded ? imgThumbnail : showUploadSpinner"
                ></ng-container>
              }
              @case (fileTypes.VIDEO) {
                <ng-container 
                  [ngTemplateOutlet]="fileUploaded ? videoThumbnail : showUploadSpinner"
                ></ng-container>
              }
            }
          }
      
            <div class="chat-input-wrapper">
              <input 
                matInput 
                formControlName="message" 
                placeholder="Type a message..." 
                (keyup.enter)="!isFileUploading && onSendMessage()" 
              />
              <div class="chat-input-btns">
                <img 
                  [src]="constants.staticImages.icons.attachmentClip" 
                  class="me-3 pointer" alt=""
                  (click)="toggleAttachmentMenu()" 
                />
                <button 
                  mat-raised-button 
                  color="primary" 
                  class="mat-primary-btn" 
                  type="button" 
                  [disabled]="isFileUploading"
                  (click)="onSendMessage()"
                >
                  <img [src]="constants.staticImages.icons.sendIcon" alt="" />
                </button>
      
                <div class="attachment-menu" *ngIf="isAttachmentMenuOpen">
                  <div (click)="imgInput.click()" class="attachment-option">
                    <mat-icon>photo_library</mat-icon> Photo
                  </div>
                  <input 
                    type="file" 
                    accept="image/*" 
                    #imgInput 
                    [hidden]="true" 
                    [max]="1"
                    (change)="onFileSelected($event)" 
                  />
                  
                  <div (click)="videoInput.click()" class="attachment-option">
                    <mat-icon>video_library</mat-icon> Video
                  </div>
                  <input 
                    type="file" 
                    accept="video/*" 
                    #videoInput 
                    [hidden]="true" 
                    [max]="1"
                    (change)="onFileSelected($event)" 
                  />
      
                  <div (click)="docInput.click()" class="attachment-option">
                    <mat-icon>insert_drive_file</mat-icon> Document
                  </div>
                  <input 
                    type="file" 
                    accept=".pdf, .doc, .docx, .xls, .xlsx, .txt" 
                    #docInput 
                    [hidden]="true" 
                    [max]="1"
                    (change)="onFileSelected($event)" 
                  />
                </div>
              </div>
            </div>
          </div>
        </form>
      }
    </div>    
</div>

<ng-template #imgThumbnail>
  <div class="uploaded-file">
    <img [src]="fullPath" alt="" />
    <mat-icon (click)="removeFileFromAws()">close</mat-icon>
  </div>
</ng-template>

<ng-template #videoThumbnail>
  <div class="uploaded-file">
    <video [src]="fullPath" controls></video>
    <mat-icon (click)="removeFileFromAws()">close</mat-icon>
  </div>
</ng-template>

<ng-template #docThumbnail>
  <div class="file-detail-wrapper">
    <div class="file-detail-content">
      @if(fileExtension.includes('pdf')) {
      <img [src]="constants.staticImages.images.pdfImage" alt="" />
      }
      @else if(fileExtension.includes('doc')) {
      <img [src]="constants.staticImages.images.docImage" alt="" />
      }
      @else if(fileExtension.includes('xls')) {
      <img [src]="constants.staticImages.images.xlsImage" alt="" />
      }
      @else if(fileExtension.includes('txt')) {
      <img [src]="constants.staticImages.images.txtImage" alt="" />
      }
      <span class="text-truncate ps-2">{{ fileName }}</span>
    </div>
    <mat-icon (click)="removeFileFromAws()">close</mat-icon>
  </div>
</ng-template>

<ng-template #showUploadSpinner>
  <div class="upload-spinner-wrapper">
    <div class="loader"></div>
  </div>
</ng-template>

<ng-template #showLoader>
  <app-content-loader></app-content-loader>
</ng-template>
