<mat-sidenav-container class="example-container" [hasBackdrop]="true">
  <mat-sidenav
    [opened]="isSupervisorSideNavOpen"
    mode="over"
    position="end"
    fixedInViewport="true"
    [ngClass]="isAddSupervisor ? 'sidebar-w-850' : 'lg-sidebar'"
    [disableClose]="true">
    <ng-container [ngTemplateOutlet]="isAddSupervisor ? addSupervisor : viewSupervisor"></ng-container>
  </mat-sidenav>

  <mat-sidenav-content>
    <div class="header-tab-only-btn" *appHasPermission="[constants.roles.ADMIN, constants.roles.DESK_MANAGER]">
      <button
        mat-raised-button
        color="primary"
        class="mat-primary-btn action-btn"
        type="button"
        (click)="openAddOrViewSupervisor(true, null)">
        Add Supervisor
      </button>
    </div>

    <div class="auth-page-wrapper auth-page-with-header">
      <div class="filter-and-count-wrapper mb-2">
        <div class="search-and-count-wrapper-auth">
          <div class="total-users">
            Total: <span>{{ totalCount }}</span>
          </div>
          <div class="search-bar">
            <mat-form-field class="search-bar-wrapper ms-3">
              <input
                matInput
                placeholder="Search.."
                [(ngModel)]="filters.searchTerm"
                (ngModelChange)="onSearchTermChanged()" />
              <mat-icon matTextPrefix>search</mat-icon>
            </mat-form-field>
          </div>
        </div>
        <div class="filter-wrapper">
          <div class="search-bar-wrapper">
            <app-multi-select
              [filterDetail]="filters.locationId"
              (selectedFilterValues)="getSupervisors((currentPage = 1), pageSize)">
            </app-multi-select>
          </div>

          <div class="search-bar-wrapper">
            <app-multi-select
              [filterDetail]="filters.instrumentId"
              (selectedFilterValues)="getSupervisors((currentPage = 1), pageSize)">
            </app-multi-select>
          </div>
        </div>
      </div>

      <div class="o-card">
        <div class="o-card-body">
          <div class="o-table">
            <div class="o-row o-header">
              <div class="o-cell first-cell">Supervisor</div>
              <div class="o-cell">No. of Instructors</div>
              <div class="o-cell">Location</div>
              <div class="o-cell">Instrument</div>
            </div>
            <div class="dotted-divider"></div>
            <div class="content" [ngClass]="totalCount > 10 ? 'show-pagination' : 'hide-pagination'">
              <ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : supervisorTemplate"></ng-container>
            </div>
          </div>
        </div>
      </div>
      @if (totalCount > 10) {
        <pagination-controls
          id="supervisor"
          [previousLabel]="''"
          [nextLabel]="''"
          (pageChange)="onPageChange($event)"
          [responsive]="true"
          class="pagination-controls"></pagination-controls>
      }
    </div>
  </mat-sidenav-content>
</mat-sidenav-container>

<ng-template #addSupervisor>
  <app-add-instructor
    [isSupervisor]="true"
    [selectedInstructorDetails]="selectedInstructorViewDetails"
    (closeSideNav)="closeAddOrEditSupervisor($event)"
    (isInstructorAdded)="getSupervisors(currentPage, pageSize)"></app-add-instructor>
</ng-template>

<ng-template #viewSupervisor>
  <app-view-instructor
    [isSupervisor]="true"
    (openEditSideNav)="openAddOrViewSupervisor(true, selectedInstructorViewDetails)"
    (closeViewSideNav)="closeAddOrEditSupervisor(null)"
    (refreshInstructors)="getSupervisors((currentPage = 1), pageSize)"
    [selectedInstructorViewDetails]="selectedInstructorViewDetails"></app-view-instructor>
</ng-template>

<ng-template #supervisorTemplate>
  <ng-container [ngTemplateOutlet]="totalCount ? supervisorTableContent : noDataFound"></ng-container>
</ng-template>

<ng-template #supervisorTableContent>
  @for (
    supervisor of supervisors
      | paginate: { itemsPerPage: pageSize, currentPage: currentPage, totalItems: totalCount, id: "supervisor" };
    track $index
  ) {
    <div class="o-row">
      <div
        class="o-cell first-cell instructor-name-photo-wrapper"
        (click)="openInstructorDetails(supervisor.instructorDetail.id)">
        @if (supervisor.instructorDetail.profilePhoto) {
          <img [src]="supervisor.instructorDetail.profilePhoto" class="me-2" alt="" />
        } @else {
          <div class="placeholder-name">
            <div>
              {{ getInitials(supervisor.instructorDetail.name) | uppercase }}
            </div>
          </div>
        }
        <div class="text-truncate" [matTooltip]="supervisor.instructorDetail.name">{{ supervisor.instructorDetail.name }}</div> 
      </div>
      <div class="o-cell text-gray">{{ supervisor.instructorDetail.instructors }}</div>
      <div class="o-cell text-gray">
        <div class="instrument-wrapper">
          @if (supervisor.instructorDetail.instructorAvailability.length) {
            <img [src]="constants.staticImages.icons.location" class="me-1" alt="" />
            @for (instructorAvailability of supervisor.instructorDetail.instructorAvailability; track $index) {
              <div class="instrument-item" *ngIf="$index < 1">{{ instructorAvailability.locationName }}</div>
              <div
                class="dot"
                *ngIf="
                  $index < 1 &&
                  getLocationDetails(supervisor.instructorDetail.instructorAvailability).count - 1 !== $index
                "></div>
            }
            @if (getLocationDetails(supervisor.instructorDetail.instructorAvailability).count > 1) {
              <div
                class="remaining-instrument-available-count"
                [matTooltip]="getLocationDetails(supervisor.instructorDetail.instructorAvailability).names">
                {{ getLocationDetails(supervisor.instructorDetail.instructorAvailability).count - 1 }}+
              </div>
            }
          } @else {
            -
          }
        </div>
      </div>
      <div class="o-cell text-gray">
        <div class="instrument-wrapper">
          @if (supervisor.instructorDetail.instruments.length) {
            @for (instrument of supervisor.instructorDetail.instruments; track $index) {
              <div class="instrument-item" *ngIf="$index < 1">{{ instrument.name }}</div>
              <div
                class="dot"
                *ngIf="$index < 1 && supervisor.instructorDetail.instruments.length - 1 !== $index"></div>
            }
            @if (supervisor.instructorDetail.instruments.length > 1) {
              <div
                class="remaining-instrument-available-count"
                [matTooltip]="getInstrumentNames(supervisor.instructorDetail.instruments)">
                {{ supervisor.instructorDetail.instruments.length - 1 }}+
              </div>
            }
          } @else {
            -
          }
        </div>
      </div>
    </div>

    @if ($index < supervisors.length - 1) {
      <div class="dotted-divider"></div>
    }
  }
</ng-template>

<ng-template #noDataFound>
  <div class="no-data-found-wrapper">
    <h3>NO DATA FOUND!</h3>
  </div>
</ng-template>

<ng-template #showLoader>
  <div class="page-loader-wrapper">
    <app-content-loader></app-content-loader>
  </div>
</ng-template>
