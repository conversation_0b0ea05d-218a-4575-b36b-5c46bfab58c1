<mat-sidenav-container class="example-container" [hasBackdrop]="true">
  @if (isViewMaintenanceRequest) {
  <mat-sidenav
    [opened]="isViewMaintenanceRequest"
    mode="over"
    position="end"
    fixedInViewport="true"
    [ngClass]="'sidebar-w-750'"
    [disableClose]="true"
  >
    <app-view-maintenance-request
      (closeModal)="isViewMaintenanceRequest = false"
      [selectedMaintenanceRequestDetails]="selectedMaintenanceRequestDetails"
    ></app-view-maintenance-request>
  </mat-sidenav>
  }

  <mat-sidenav-content>
    <div class="auth-page-wrapper auth-page-with-header">
      <div class="filter-and-count-wrapper">
        <div class="search-and-count-wrapper-auth">
          <div class="total-users">
            Total: <span>{{ maintenanceRequests?.length }}</span>
          </div>
          <div class="search-bar">
            <mat-form-field class="search-bar-wrapper ms-3">
              <input matInput placeholder="Search.." [(ngModel)]="searchTerm" />
              <mat-icon matTextPrefix>search</mat-icon>
            </mat-form-field>
          </div>
        </div>
        <div class="filter-wrapper"></div>
      </div>

      <div class="o-card">
        <div class="o-card-body">
          <div class="o-table">
            <div class="o-row o-header">
              <div class="o-cell">Request Summary</div>
              <div class="o-cell first-cell">Created on</div>
              <div class="o-cell">Instrument</div>
              <div class="o-cell">Location</div>
              <div class="o-cell">Room No.</div>
              <div class="o-cell">Status</div>
            </div>
            <div class="dotted-divider"></div>
            <div class="content">
              <ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : closedRequestTemplate"></ng-container>
            </div>
          </div>
        </div>
      </div>
    </div>
  </mat-sidenav-content>
</mat-sidenav-container>

<ng-template #closedRequestTemplate>
  <ng-container [ngTemplateOutlet]="maintenanceRequests?.length ? closedMaintenanceRequestTableContent : noDataFound"></ng-container>
</ng-template>

<ng-template #closedMaintenanceRequestTableContent>
  @if (maintenanceRequests?.length) { @for ( maintenanceRequest of maintenanceRequests! | filterByTitle: searchTerm; track $index ) {
  <div class="o-row">
    <div class="o-cell text-gray pointer" (click)="openViewMaintenanceModal(maintenanceRequest)">{{ maintenanceRequest.title }}</div>
    <div class="o-cell text-gray pointer" (click)="openViewMaintenanceModal(maintenanceRequest)">
      <div>{{ maintenanceRequest.createdOn | localDate | date : constants.dateFormats.MMM_D_Y }}</div>
      <div>
        From <span class="text-black">{{ maintenanceRequest.createdByName }}</span>
      </div>
    </div>
    <div class="o-cell text-gray pointer" (click)="openViewMaintenanceModal(maintenanceRequest)">
      {{ maintenanceRequest.instrumentName }}
    </div>
    <div class="o-cell text-gray">
      <div class="d-flex justify-content-center align-items-center">
        <img [src]="constants.staticImages.icons.location" alt="" />
        {{ maintenanceRequest.locationName }}
      </div>
    </div>
    <div class="o-cell text-gray">{{ maintenanceRequest.roomName }}</div>
    <div class="o-cell text-gray">
      <div class="o-cell text-gray">
        @switch (maintenanceRequest.status) { @case (maintenanceRequestsStatus.InProgress) {
        <span class="in-progress-request">{{ maintenanceRequestsStatusLabel.InProgress }}</span>
        } @case (maintenanceRequestsStatus.OnHold) {
        <span class="on-hold-request">{{ maintenanceRequestsStatusLabel.OnHold }}</span>
        } @case (maintenanceRequestsStatus.Open) {
        <span class="open-request">{{ maintenanceRequestsStatusLabel.Open }}</span>
        }@default {
        <span class="text-black">{{ maintenanceRequestsStatusLabel.Closed }}</span>
        } }
      </div>
      <div *ngIf="maintenanceRequest.status === maintenanceRequestsStatus.Closed">
        By <span class="text-black">{{ maintenanceRequest.closedByName }}</span>
      </div>
    </div>
  </div>

  @if ($index < maintenanceRequests!.length - 1) {
  <div class="dotted-divider"></div>
  } }}
</ng-template>

<ng-template #noDataFound>
  <div class="no-data-found-wrapper">
    <h3>NO DATA FOUND!</h3>
  </div>
</ng-template>

<ng-template #showLoader>
  <div class="page-loader-wrapper">
    <app-content-loader></app-content-loader>
  </div>
</ng-template>
