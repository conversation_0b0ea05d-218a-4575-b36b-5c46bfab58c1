<div class="week-view-wrapper" [ngClass]="{ 'no-header': currentUser$?.userRoleId === constants.roleIds.INSTRUCTOR || currentUser$?.userRoleId === constants.roleIds.SUPERVISOR }">
  <ng-container [ngTemplateOutlet]="isLoading ? showLoader : showWeekView"></ng-container>
</div>

<ng-template #showWeekView>
  <mbsc-eventcalendar
    [data]="schedulerData"
    [options]="calendarOptions"
    themeVariant="light"
    theme="ios"
    (onCellDoubleClick)="onOpenAddSchedule()"
    [scheduleEventTemplate]="eventTemplate"
    [selectedDate]="showScheduleForDate">
  </mbsc-eventcalendar>
</ng-template>

<ng-template #eventTemplate let-data>
  <ng-container
    [ngTemplateOutlet]="eventInfoTemplate"
    [ngTemplateOutletContext]="{ data: data, showMoreEventPopup: false }"></ng-container>
</ng-template>

<ng-template #showLoader>
  <div class="page-loader-wrapper">
    <app-content-loader></app-content-loader>
  </div>
</ng-template>

<mbsc-popup class="md-tooltip" #showMoreEventsPopup [anchor]="showMoreEventsAnchor" [options]="popupOptions">
  <div class="p-2">
    @for (data of showMoreEventList; track $index) {
      <ng-container
        [ngTemplateOutlet]="eventInfoTemplate"
        [ngTemplateOutletContext]="{ data: data, showMoreEventPopup: true }"></ng-container>
    }
  </div>
</mbsc-popup>

<mbsc-popup
  class="md-tooltip events-detail-popup"
  #eventDetailsPopup
  [anchor]="detailsAnchor"
  [options]="popupOptions"
  (onClose)="schedulerDetailPopupComponent.showCancelLessonView = false">
  <app-scheduler-detail-popup
    [selectedEvent]="selectedEvent"
    (editLesson)="onEditLesson($event)"
    (closePopup)="closeEventDetailsPopup($event)"></app-scheduler-detail-popup>
</mbsc-popup>

<ng-template #eventInfoTemplate let-data="data" let-showMoreEventPopup="showMoreEventPopup">
  @if (data.original.isLeave) {
    <div
      (click)="openEventDetailsPopup(data.original, $event)"
      [ngStyle]="{
        background: data.original.color || constants.colors.leaveBgColor
      }"
      [ngClass]="{ 'schedule-item': true, 'schedule-popup-events': showMoreEventPopup, 'leave-event': true }">
      <div class="schedule-border" [ngStyle]="{ 'background-color': constants.colors.leaveBorderColor }"></div>
      <div class="schedule-info-wrapper" [ngStyle]="{ color: 'white' }">
        <div class="lesson-name">{{ data.original.title || data.original.name + ' - OUT' }}</div>
        <div class="leave-reason" [matTooltip]="data.original.reason">{{ data.original.reason }}</div>
      </div>
    </div>
  } @else {
    <div
      (click)="openEventDetailsPopup(data.original, $event)"
      [ngStyle]="{
        background: schedulerService.getScheduleBackgroundColor(
          data.original.start,
          data.original.isDraftSchedule,
          data.original.instrumentColor,
          data.original.classType
        )
      }"
      [ngClass]="{ 'schedule-item': true, 'schedule-popup-events': showMoreEventPopup }">
    <div
      class="schedule-border"
      [ngStyle]="{
        'background-image': schedulerService.getScheduleBorderImage(
          data.original.start,
          data.original.isDraftSchedule,
          data.original.classType
        ),
        'background-color': schedulerService.getScheduleColor(
          data.original.start,
          data.original.isDraftSchedule,
          data.original.instrumentFontColor,
          data.original.classType
        )
      }"></div>
    <div
      class="schedule-info-wrapper"
      [ngStyle]="{
        color: schedulerService.getScheduleColor(
          data.original.start,
          data.original.isDraftSchedule,
          data.original.instrumentFontColor,
          data.original.classType
        )
      }">
      <div class="lesson-name" *ngIf="data.original.classType === classTypes.SUMMER_CAMP">
        Summer Camp ({{ getDayInRange(data.original.campStartDate, data.original.start) }}d)
      </div>
      <div class="lesson-name" [ngClass]="{ strike: data.original.isCancelSchedule }">
        <span
          [ngClass]="data.original.classType === classTypes.SUMMER_CAMP ? 'camp-name' : 'lesson-name'"
          *ngIf="data.original.isCancelSchedule"
          >
          @if (data.original.isLateCancelSchedule) {
            Late Canceled:
          } @else {
            Canceled:
          }
        </span>
        @switch (data.original.classType) {
          @case (classTypes.GROUP_CLASS) {
            {{ data.original.groupClassName | titlecase }} ({{ getTimeDiff(data.original.start, data.original.end) }})
          }
          @case (classTypes.ENSEMBLE_CLASS) {
            {{ data.original.ensembleClassName | titlecase }} ({{ getTimeDiff(data.original.start, data.original.end) }})
          }
          @case (classTypes.SUMMER_CAMP) {
            {{ data.original.campName | titlecase }} ({{
              schedulerService.getNumberOfDays(data.original.campStartDate, data.original.campEndDate)
            }}d)
          }
          @case (classTypes.MAKE_UP) {
            {{ data.original.instrumentName }} Make-Up Lesson ({{
              getTimeDiff(data.original.start, data.original.end)
            }})
          }
          @case (classTypes.INTRODUCTORY) {
            Introductory {{ data.original.instrumentName }} Lesson ({{
              getTimeDiff(data.original.start, data.original.end)
            }})
          }
          @default {
            {{ data.original.instrumentName }} Lesson ({{ getTimeDiff(data.original.start, data.original.end) }})
          }
        }
      </div>
      @if (data.original.classType === classTypes.SUMMER_CAMP && data.original.studentDetails?.length) {
        <div class="instructor-info-wrapper">
          <img class="student-img" [attr.alt]="data.title" [src]="constants.staticImages.icons.memberIcon" />
          <div
            class="instructor-name"
            [ngStyle]="{
              color: schedulerService.isPastDate(data.original.start)
                ? constants.disabledEventColors.color
                : 'instructor-name'
            }">
            {{ data.original.studentDetails[0].studentName }}
            @if (data.original.studentDetails.length > 1) {
              +{{ data.original.studentDetails.length - 1 }}
            }
          </div>
        </div>
      } @else {
        <div class="instructor-info-wrapper">
          <img
            class="instructor-img"
            [attr.alt]="data.title"
            [src]="constants.staticImages.images.profileImgPlaceholder" />
          <div
            class="instructor-name"
            [ngStyle]="{
              color: schedulerService.isPastDate(data.original.start)
                ? constants.disabledEventColors.color
                : 'instructor-name'
            }">
                @if (data.original.classType === classTypes.ENSEMBLE_CLASS) {
                  @for (assignedInstructors of data.original.assignedInstructors; track $index) {
                  <ng-container *ngIf="$index < 1">
                     {{ assignedInstructors?.instructorName }}
                    <div class="dot hide-sm-screen-devices" *ngIf="!$last"></div>
                  </ng-container>
                  } @if (data.original.assignedInstructors?.length>1) {
                  <div class="remaining-instrument-available-count" [matTooltip]="getInstructorNames(data.original.assignedInstructors)">
                    {{ data.original.assignedInstructors!.length - 1}}+
                  </div>
                  } } @else{
                  {{ data.original.instructorName }}
                  }
          </div>
        </div>
      }
    </div>
    <div class="draft-badge" *ngIf="data.original.isDraftSchedule">
      <img [src]="constants.staticImages.icons.draftBadge" height="22" alt="" />
    </div>
    </div>
  }
</ng-template>
