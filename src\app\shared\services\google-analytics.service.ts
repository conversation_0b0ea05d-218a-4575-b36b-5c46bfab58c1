import { Injectable, Inject, Renderer2, RendererFactory2 } from '@angular/core';
import { DOCUMENT } from '@angular/common';
import { environment } from 'src/environments/environment';
import { Constants } from '../constants';
 
export interface EcommerceItem {
  item_id?: string;
  item_name?: string;
  affiliation?: string;
  coupon?: string;
  discount?: number;
  index?: number;
  item_brand?: string;
  item_category?: string;
  item_category2?: string;
  item_category3?: string;
  item_category4?: string;
  item_category5?: string;
  item_list_id?: string;
  item_list_name?: string;
  item_variant?: string;
  location_id?: string;
  price: number;
  quantity: number;
  // Custom parameters
  instructor_id?: string;
  appt_schedule_start?: string;
  appt_schedule_end?: string;
  discount_type?: string;
}
 
export interface EcommerceEvent {
  currency: string;
  value: number;
  items: EcommerceItem[];
}
 
@Injectable({
  providedIn: 'root'
})
export class GoogleAnalyticsService {
  private renderer: Renderer2;
  private isGtmInitialized = false;
 
  constructor(
    @Inject(DOCUMENT) private document: Document,
    private rendererFactory: RendererFactory2
  ) {
    this.renderer = this.rendererFactory.createRenderer(null, null);
    this.initializeGTM();
  }
  private readonly locationIds = {
    octopusmusicschoolhillsborough: 'ChIJy3HqtNHrw4kReOEi7l51Z7I',
    octopusmusicschoolsouthplainfield: 'ChIJv4pEX3a3w4kRcXZgDLGgumM',
    octopusmusicschoolnorthbrunswick: 'ChIJTV2wR4rDw4kRK1PrDyCtg3o',
    octopusmusicschoolmiddletown: 'ChIJPVkUrq4xwokRRIq_i7j59Gc'
  };
 
  private initializeGTM(): void {
    if (typeof window !== 'undefined' && !this.isGtmInitialized) {
      this.addGtmScript();
      this.addGtmNoscript();
      this.isGtmInitialized = true;
    }
  }
 
  private addGtmScript(): void {
    try {
      // Initialize dataLayer if it doesn't exist
      if (!(window as any).dataLayer) {
        (window as any).dataLayer = [];
      }
 
      // Create the script element
      const script = this.renderer.createElement('script');
 
      // Set attributes for the script
      this.renderer.setAttribute(script, 'type', 'text/javascript');
 
      // Create the script content
      const scriptContent = `
        (function (w, d, s, l, i) {
          w[l] = w[l] || [];
          w[l].push({ 'gtm.start': new Date().getTime(), event: 'gtm.js' });
          var f = d.getElementsByTagName(s)[0],
            j = d.createElement(s),
            dl = l != 'dataLayer' ? '&l=' + l : '';
          j.async = true;
          j.src = 'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
          f.parentNode.insertBefore(j, f);
        })(window, document, 'script', 'dataLayer', '${environment.gtmContainerId}');
      `;
 
      // Set the script content
      this.renderer.setProperty(script, 'innerHTML', scriptContent);
 
      // Append the script to the head
      this.renderer.appendChild(this.document.head, script);
    } catch (error) {
      console.error('GA-addGtmScript', error);
    }
  }
 
  private addGtmNoscript(): void {
    try {
      // Create the noscript element
      const noscript = this.renderer.createElement('noscript');
 
      // Create the iframe element
      const iframe = this.renderer.createElement('iframe');
 
      // Set attributes for the iframe
      this.renderer.setAttribute(iframe, 'src', `https://www.googletagmanager.com/ns.html?id=${environment.gtmContainerId}`);
      this.renderer.setAttribute(iframe, 'height', '0');
      this.renderer.setAttribute(iframe, 'width', '0');
      this.renderer.setStyle(iframe, 'display', 'none');
      this.renderer.setStyle(iframe, 'visibility', 'hidden');
 
      // Append the iframe to the noscript
      this.renderer.appendChild(noscript, iframe);
 
      // Append the noscript to the body
      this.renderer.appendChild(this.document.body, noscript);
    } catch (error) {
      console.error('GA-addGtmNoscript', error);
    }
  }

  trackAddToCart(eventData: EcommerceEvent): void {
    this.pushEvent('add_to_cart', eventData);
  }

  trackRemoveFromCart(eventData: EcommerceEvent): void {
    this.pushEvent('remove_from_cart', eventData);
  }

  trackBeginCheckout(eventData: EcommerceEvent): void {
    this.pushEvent('begin_checkout', eventData);
  }

  trackPurchase(eventData: EcommerceEvent): void {
    this.pushEvent('purchase', eventData);
  }
 
  getLocationId(locationName: string): string {
    const normalizedName = locationName.toLowerCase().replace(/\s+/g, '');
    return this.locationIds[normalizedName as keyof typeof this.locationIds] || '';
  }

  createEcommerceItem(item: any): EcommerceItem {
    return {
      item_name: item?.name,
      item_category: item?.instrumentName || '',
      location_id: item?.locationName ? this.getLocationId(item.locationName) : undefined,
      price: item?.productPrice || item?.price,
      quantity: item?.quantity || 1,
      discount: item?.discount || 0,
      instructor_id: item?.instructorId,
      appt_schedule_start: item?.scheduleStartTime,
      appt_schedule_end: item?.scheduleEndTime,
      discount_type: item.discount ? item?.isRecurringDiscount ? 'recurring' : 'one_time' : ''
    };
  }
 
  private pushEvent(eventName: string, eventData: EcommerceEvent): void {
    if (typeof window !== 'undefined' && (window as any).dataLayer) {
      const eventPayload = {
        event: eventName,
        ecommerce: {
          ...eventData
        }
      };

      (window as any).dataLayer.push(eventPayload);
    } else {
      console.warn('❌ DataLayer not available for event:', eventName);
    }
  }
 
}
 